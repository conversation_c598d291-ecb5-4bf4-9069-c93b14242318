import axios from 'axios';

const BASE_URL = import.meta.env.VITE_BASE_API_URL;
const LUMUS_URL = import.meta.env.VITE_LUMUSAI_API_URL;

export const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

export const max_upload_files = import.meta.env.VITE_UPLOAD_MAX_FILES || 4;
export const max_files_size = import.meta.env.VITE_UPLOAD_MAX_FILES_SIZE || 2 * 1024 * 1024; // 2MB

export const lumusApi = axios.create({
  baseURL: LUMUS_URL,
  headers: {
    'Content-Type': 'multipart/form-data'
  }
});

export const endpoints = {
  candidates: {
    list: '/candidate/candidates_pagination/',
    validate: '/candidate/validate_candidate',
    create: '/candidate/',
    update: '/candidate/',
    get: (id: string) => `/candidate/${id}`,
    delete: (id: string) => `/candidate/${id}/delete`,
    export: (id: string) => `/candidate/${id}/export`,
    disable: (id: string) => `/candidate/${id}/disable`,
    enable: (id: string) => `/candidate/${id}/enable`,
    getRoles: '/candidate/get/roles/',
    getCountries: '/candidate/get/countries/',
    getCreatedBy: '/candidate/get/created_by/',
  },
  projects: {
    list: '/project/'
  },
  positions: {
    list: '/position/positions_pagination/',
    match: '/match',
    customMatch: '/match/custom',
    getLocations: '/position/get/locations/',
    getClients: '/position/get/clients/'
  },
  interview: {
    addCandidates: (positionId: string) => `/interview/${positionId}/create`,
    getByCandidateId: (id: string) => `/interview/?candidate_id=${id}`,
    getByPositionId: (id: string) => `/interview/${id}`,
    getById: (id: string) => `/interview/${id}`,
  },
  notes: {
    create: '/note/',
    update: (id: string) => `/note/${id}`,
    get: (id: string) => `/note/${id}`,
    delete: (id: string) => `/note/${id}`,
    getByCandidate: (candidateId: string) => `/note/candidate/${candidateId}`
  }
};
