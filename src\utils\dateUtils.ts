/**
 * Formats a date string to MM/dd/yyyy HH:mm format
 * @param dateString - ISO date string
 * @returns Formatted date string or 'N/A' if invalid
 */
export const formatDateTime = (dateString?: string): string => {
  if (!dateString) return 'N/A';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  } catch {
    return 'N/A';
  }
};

/**
 * Formats a date string to dd/mm/yyyy format for notes
 * @param dateString - ISO date string
 * @returns Formatted date string or 'N/A' if invalid
 */
export const formatNoteDate = (dateString?: string): string => {
  if (!dateString) return 'N/A';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  } catch {
    return 'N/A';
  }
};

/**
 * Checks if a value is meaningful (not null, undefined, empty string, or just whitespace)
 * @param value - Value to check
 * @returns True if value is meaningful
 */
export const hasValue = (value: any): boolean => {
  if (value === null || value === undefined) return false;
  if (typeof value === 'string') return value.trim().length > 0;
  if (Array.isArray(value)) return value.length > 0;
  return true;
};

/**
 * Checks if a URL is valid and clickable
 * @param url - URL string to validate
 * @returns True if URL is valid
 */
export const isValidUrl = (url?: string): boolean => {
  if (!url) return false;
  try {
    const validUrl = url.startsWith('http') ? url : `https://${url}`;
    new URL(validUrl);
    return true;
  } catch {
    return false;
  }
}; 