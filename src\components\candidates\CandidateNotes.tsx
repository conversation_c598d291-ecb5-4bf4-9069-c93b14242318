import React, { useState, useEffect } from 'react';
import { Card, Button, Input, message, Spin, Empty, Typography, Popconfirm, Space } from 'antd';
import { EditOutlined, DeleteOutlined, CalendarOutlined, PlusOutlined, SaveOutlined, CloseOutlined } from '@ant-design/icons';
import { useMsal } from '@azure/msal-react';
import { Note } from '../../types/candidate';
import { notesService } from '../../services/notesService';
import { formatNoteDate } from '../../utils/dateUtils';

const { TextArea } = Input;
const { Text, Title } = Typography;

interface CandidateNotesProps {
  candidateId: string;
  candidateInfo: any;
}

export const CandidateNotes: React.FC<CandidateNotesProps> = ({ candidateId, candidateInfo }) => {
  const { instance } = useMsal();
  const activeAccount = instance.getActiveAccount();
  
  // Handle local development where user might not be authenticated
  const isLocalDev = (import.meta as any).env?.DEV && (
    window.location.hostname === 'localhost' || 
    window.location.hostname === '127.0.0.1'
  );
  
  const currentUserEmail = activeAccount?.username || 
                          activeAccount?.homeAccountId || 
                          (isLocalDev ? '<EMAIL>' : '<EMAIL>');
  
  const [notes, setNotes] = useState<Note[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingNoteId, setEditingNoteId] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');
  const [newNoteContent, setNewNoteContent] = useState('');
  const [showAddNote, setShowAddNote] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  // Fetch notes for the candidate
  const fetchNotes = async () => {
    setLoading(true);
    try {
      const notes = await notesService.getNotesByCandidate(candidateId);
      setNotes(notes.sort((a: Note, b: Note) => 
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      ));
    } catch (error) {
      console.error('Error fetching notes:', error);
      message.error('Failed to load notes');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchNotes();
  }, [candidateId]);

  // Create a new note
  const handleCreateNote = async () => {
    if (!newNoteContent.trim()) {
      message.warning('Please enter note content');
      return;
    }

    setSubmitting(true);
    try {
      await notesService.createNote({
        candidate_id: candidateId,
        notes: {
          note: newNoteContent.trim()
        },
        created_by: currentUserEmail,
        created_at: new Date().toISOString()
      });
      
      message.success('Note created successfully');
      setNewNoteContent('');
      setShowAddNote(false);
      fetchNotes();
    } catch (error) {
      console.error('Error creating note:', error);
      message.error('Failed to create note');
    } finally {
      setSubmitting(false);
    }
  };

  // Update an existing note
  const handleUpdateNote = async (noteId: string) => {
    if (!editContent.trim()) {
      message.warning('Please enter note content');
      return;
    }

    setSubmitting(true);
    try {
      await notesService.updateNote(noteId, {
        notes: {
          note: editContent.trim()
        },
        updated_by: currentUserEmail
      });
      
      message.success('Note updated successfully');
      setEditingNoteId(null);
      setEditContent('');
      fetchNotes();
    } catch (error) {
      console.error('Error updating note:', error);
      message.error('Failed to update note');
    } finally {
      setSubmitting(false);
    }
  };

  // Delete a note
  const handleDeleteNote = async (noteId: string) => {
    try {
      await notesService.deleteNote(noteId);
      message.success('Note deleted successfully');
      fetchNotes();
    } catch (error) {
      console.error('Error deleting note:', error);
      message.error('Failed to delete note');
    }
  };

  // Start editing a note
  const startEditing = (note: Note) => {
    setEditingNoteId(note.id);
    setEditContent(note.notes?.note || '');
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditingNoteId(null);
    setEditContent('');
  };



  if (loading) {
    return <Spin style={{ display: 'block', textAlign: 'center', margin: '2rem 0' }} />;
  }

  return (
    <div style={{ padding: '0 24px' }}>
      {/* Add Note Section */}
      <div style={{ marginBottom: 24 }}>
        {!showAddNote ? (
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => setShowAddNote(true)}
            style={{ 
              borderRadius: 6,
              backgroundColor: '#6366f1',
              borderColor: '#6366f1',
              fontSize: 14,
              height: 'auto',
              padding: '8px 16px'
            }}
          >
            Add Note
          </Button>
        ) : (
          <div
            style={{ 
              borderRadius: 8,
              border: '1px solid #e0f2fe',
              backgroundColor: '#f0f9ff',
              padding: 16
            }}
          >
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: 16 
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <Text style={{ margin: 0, color: '#000', fontSize: 16, fontWeight: 'bold' }}>
                  Note {notes.length + 1}
                </Text>
                <Text style={{ margin: 0, color: '#666', fontSize: 14 }}>
                  {formatNoteDate(new Date().toISOString())}
                </Text>
              </div>
              <Space>
                <SaveOutlined 
                  style={{ 
                    fontSize: 16, 
                    color: '#6366f1', 
                    cursor: 'pointer',
                    opacity: submitting ? 0.5 : 1
                  }}
                  onClick={handleCreateNote}
                />
                <CloseOutlined 
                  style={{ 
                    fontSize: 16, 
                    color: '#666', 
                    cursor: 'pointer' 
                  }}
                  onClick={() => {
                    setShowAddNote(false);
                    setNewNoteContent('');
                  }}
                />
              </Space>
            </div>
            <TextArea
              rows={4}
              placeholder="Write here your note"
              value={newNoteContent}
              onChange={(e) => setNewNoteContent(e.target.value)}
              style={{ 
                borderRadius: 6, 
                border: '1px solid #e5e7eb',
                backgroundColor: '#f9fafb',
                padding: 12,
                fontSize: 14,
                lineHeight: 1.6
              }}
            />
          </div>
        )}
      </div>

      {/* Notes List */}
      {notes.length === 0 ? (
        <Empty 
          description="No notes available" 
          style={{ margin: '2rem 0' }}
        />
      ) : (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
          {notes.map((note, index) => (
                         <div
               key={note.id}
               style={{ 
                 borderRadius: 8,
                 border: '1px solid #e0f2fe',
                 backgroundColor: '#f0f9ff',
                 padding: 16
               }}
             >
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                marginBottom: 12 
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <Text style={{ margin: 0, color: '#000', fontSize: 16, fontWeight: 'bold' }}>
                    Note {notes.length - index}
                  </Text>
                  <Text style={{ margin: 0, color: '#666', fontSize: 14 }}>
                    {formatNoteDate(note.created_at)}
                  </Text>
                </div>
                {editingNoteId !== note.id ? (
                  <Space>
                    <EditOutlined 
                      style={{ 
                        fontSize: 16, 
                        color: '#666', 
                        cursor: 'pointer' 
                      }}
                      onClick={() => startEditing(note)}
                    />
                    {/* Only show delete button if current user created the note */}
                    {note.created_by === currentUserEmail && (
                      <Popconfirm
                        title="Delete Note"
                        description="Are you sure you want to delete this note?"
                        onConfirm={() => handleDeleteNote(note.id)}
                        okText="Yes"
                        cancelText="No"
                        okButtonProps={{ 
                          style: { 
                            backgroundColor: '#ff4d4f', 
                            borderColor: '#ff4d4f' 
                          } 
                        }}
                      >
                        <DeleteOutlined 
                          style={{ 
                            fontSize: 16, 
                            color: '#ff4d4f', 
                            cursor: 'pointer' 
                          }}
                        />
                      </Popconfirm>
                    )}
                  </Space>
                ) : (
                     <Space>
                       <SaveOutlined 
                         style={{ 
                           fontSize: 16, 
                           color: '#6366f1', 
                           cursor: 'pointer',
                           opacity: submitting ? 0.5 : 1
                         }}
                         onClick={() => handleUpdateNote(note.id)}
                       />
                       <CloseOutlined 
                         style={{ 
                           fontSize: 16, 
                           color: '#666', 
                           cursor: 'pointer' 
                         }}
                         onClick={cancelEditing}
                       />
                     </Space>
                )}
                   </div>
              
                             {editingNoteId === note.id ? (
                   <TextArea
                     rows={4}
                     value={editContent}
                     onChange={(e) => setEditContent(e.target.value)}
                     style={{ 
                       borderRadius: 6, 
                     border: '1px solid #e5e7eb',
                     backgroundColor: '#f9fafb',
                     padding: 12,
                       fontSize: 14,
                       lineHeight: 1.6
                     }}
                   />
               ) : (
                <div>
                  <Text style={{ 
                    display: 'block', 
                    lineHeight: 1.6, 
                    color: '#262626',
                    fontSize: 14,
                    whiteSpace: 'pre-wrap'
                  }}>
                    {note.notes?.note || 'No content'}
                  </Text>
                  <div style={{ marginTop: 12, fontSize: 12, color: '#666' }}>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      Added by: {note.created_by || 'Unknown'}
                    </Text>
                    {note.updated_at && note.updated_at !== note.created_at && note.updated_by && (
                      <Text type="secondary" style={{ fontSize: 12, marginLeft: 16 }}>
                        Last modified by: {note.updated_by}
                      </Text>
                    )}
                    {note.updated_at && note.updated_at !== note.created_at && (
                      <Text type="secondary" style={{ fontSize: 12, marginLeft: note.updated_by ? 16 : 0, display: 'block', marginTop: 4 }}>
                        Last modified: {formatNoteDate(note.updated_at)}
                      </Text>
                    )}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}; 