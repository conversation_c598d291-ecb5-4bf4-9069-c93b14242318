import { api, endpoints } from '../utils/api';
import type { FilterOption } from '../types/filters';

export class FilterOptionsService {
  // Cache for filter options to avoid repeated API calls
  private static cache: {
    roles?: FilterOption[];
    countries?: FilterOption[];
    createdBy?: FilterOption[];
    clients?: FilterOption[];
    locations?: FilterOption[];
  } = {};

  static async getRoles(): Promise<FilterOption[]> {
    if (this.cache.roles) {
      return this.cache.roles;
    }

    try {
      // Use the dedicated API endpoint for roles
      const response = await api.get(endpoints.candidates.getRoles);

      // The API returns an array of role strings
      const roleStrings: string[] = response.data || [];
      
      this.cache.roles = roleStrings
        .filter(role => role && role.trim()) // Filter out empty/null values
        .sort()
        .map(role => ({ label: role, value: role }));

      return this.cache.roles;
    } catch (error) {
      console.error('Error fetching roles:', error);
      return [];
    }
  }

  static async getCountries(): Promise<FilterOption[]> {
    if (this.cache.countries) {
      return this.cache.countries;
    }

    try {
      // Use the dedicated API endpoint for countries
      const response = await api.get(endpoints.candidates.getCountries);

      // The API returns an array of country strings
      const countryStrings: string[] = response.data || [];

      this.cache.countries = countryStrings
        .filter(country => country && country.trim()) // Filter out empty/null values
        .sort()
        .map(country => ({ label: country, value: country }));

      return this.cache.countries;
    } catch (error) {
      console.error('Error fetching countries:', error);
      return [];
    }
  }

  static async getCreatedBy(): Promise<FilterOption[]> {
    if (this.cache.createdBy) {
      return this.cache.createdBy;
    }

    try {
      // Use the dedicated API endpoint for created_by
      const response = await api.get(endpoints.candidates.getCreatedBy);

      // The API returns an array of created_by strings
      const createdByStrings: string[] = response.data || [];

      this.cache.createdBy = createdByStrings
        .filter(createdBy => createdBy && createdBy.trim()) // Filter out empty/null values
        .sort()
        .map(createdBy => ({ label: createdBy, value: createdBy }));

      return this.cache.createdBy;
    } catch (error) {
      console.error('Error fetching created_by:', error);
      return [];
    }
  }

  static async getClients(): Promise<FilterOption[]> {
    if (this.cache.clients) {
      return this.cache.clients;
    }

    try {
      // Use the dedicated API endpoint for clients
      const response = await api.get(endpoints.positions.getClients);

      // The API returns an array of client strings
      const clientStrings: string[] = response.data || [];
      
      this.cache.clients = clientStrings
        .filter(client => client && client.trim()) // Filter out empty/null values
        .sort()
        .map(client => ({ label: client, value: client }));

      return this.cache.clients;
    } catch (error) {
      console.error('Error fetching clients:', error);
      return [];
    }
  }

  static async getLocations(): Promise<FilterOption[]> {
    if (this.cache.locations) {
      return this.cache.locations;
    }

    try {
      // Use the dedicated API endpoint for locations
      const response = await api.get(endpoints.positions.getLocations);

      // The API returns an array of location strings
      const locationStrings: string[] = response.data || [];
      
      this.cache.locations = locationStrings
        .filter(location => location && location.trim()) // Filter out empty/null values
        .sort()
        .map(location => ({ label: location, value: location }));

      return this.cache.locations;
    } catch (error) {
      console.error('Error fetching locations:', error);
      return [];
    }
  }

  // Clear cache to force refresh
  static clearCache() {
    this.cache = {};
  }
} 