import React from 'react';
import { Progress } from 'antd';

interface ProgressCircleProps {
  percent: number;
  size?: number;
  strokeColor?: string;
  strokeWidth?: number;
  fontSize?: number;
  fontWeight?: string | number;
  textColor?: string;
  label?: string;
  labelStyle?: React.CSSProperties;
}

export const ProgressCircle: React.FC<ProgressCircleProps> = ({
  percent,
  size = 44,
  strokeColor = '#52C41A',
  strokeWidth = 6,
  fontSize = 12,
  fontWeight = 'bold',
  textColor = '#000000',
  label,
  labelStyle = {}
}) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 6 }}>
      <Progress
        type="circle"
        percent={Math.round(percent)}
        size={size}
        strokeColor={strokeColor}
        strokeWidth={strokeWidth}
        format={(percent) => (
          <span style={{ fontSize, fontWeight, color: textColor }}>
            {percent}%
          </span>
        )}
      />
      {label && (
        <span
          style={{
            fontSize: 12,
            fontWeight: 700,
            color: '#141414',
            textAlign: 'center',
            whiteSpace: 'nowrap',
            ...labelStyle
          }}
        >
          {label}
        </span>
      )}
    </div>
  );
};

export default ProgressCircle;