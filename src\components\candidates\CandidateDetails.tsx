import React, { useState } from 'react';
import { Drawer } from 'antd';
import { useFetch } from '../../hooks/useFetch';
import { LoadingState } from '../common/LoadingState';
import { CandidateContent } from './CandidateContent';
import type { Candidate, CandidateInfo } from '../../types/candidate';
import { endpoints } from '../../utils/api';

interface CandidateDetailsProps {
  full_name: string;
  candidateIdentifier: string;
  useDrawer?: boolean;
  candidateInfo?: CandidateInfo;
}

export const CandidateDetails: React.FC<CandidateDetailsProps> = ({
  full_name,
  candidateIdentifier,
  useDrawer = false,
  candidateInfo
}) => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const { data, loading } = useFetch<{ candidate: Candidate }>({
    url: endpoints.candidates.get(candidateIdentifier),
    dependencies: [candidateIdentifier, isDrawerOpen]
    //skip: !isDrawerOpen || !!candidateInfo
  });

  // if (!useDrawer) {
  //   return <CandidateContent candidateInfo={candidateInfo!} createdAt={'ssss'} />;
  // }

  return (
    <>
      <a onClick={() => setIsDrawerOpen(true)}>{full_name}</a>
      <Drawer
        width='80vw'
        placement='right'
        onClose={() => setIsDrawerOpen(false)}
        open={isDrawerOpen}
        title='Candidate Details'
      >
        {loading ? (
          <LoadingState />
        ) : (
          data && (
            <CandidateContent
              candidateInfo={data.candidate.candidate_info}
              createdAt={data.candidate.created_at ?? 'N/A'}
            />
          )
        )}
      </Drawer>
    </>
  );
};
