import { api, endpoints } from '../utils/api';
import { Note } from '../types/candidate';

export interface CreateNoteRequest {
  candidate_id: string;
  notes: {
    note?: string;
    [key: string]: any;
  };
  created_by: string;
  created_at: string;
}

export interface UpdateNoteRequest {
  notes: {
    note?: string;
    [key: string]: any;
  };
  updated_by: string;
}

export const notesService = {
  // Get all notes for a candidate
  getNotesByCandidate: async (candidateId: string): Promise<Note[]> => {
    const response = await api.get(endpoints.notes.getByCandidate(candidateId));
    return response.data;
  },

  // Create a new note
  createNote: async (noteData: CreateNoteRequest): Promise<Note> => {
    const response = await api.post(endpoints.notes.create, noteData);
    return response.data;
  },

  // Update an existing note
  updateNote: async (noteId: string, noteData: UpdateNoteRequest): Promise<Note> => {
    const response = await api.put(endpoints.notes.update(noteId), noteData);
    return response.data;
  },

  // Delete a note
  deleteNote: async (noteId: string): Promise<void> => {
    await api.delete(endpoints.notes.delete(noteId));
  },

  // Get a specific note by ID
  getNote: async (noteId: string): Promise<Note> => {
    const response = await api.get(endpoints.notes.get(noteId));
    return response.data;
  }
}; 