import React from 'react';
import './App.css';
import { Route, Routes } from 'react-router-dom';
import { Layout } from 'antd';
import { MsalProvider, AuthenticatedTemplate, UnauthenticatedTemplate, useMsal } from '@azure/msal-react';
import type { IPublicClientApplication } from '@azure/msal-browser';
import { CandidatesTable } from './components/candidates/CandidatesTable';
import { JobOrders } from './components/jobs/JobOrders';
import JobDetails from './components/jobs/JobDetails';
import { Header } from './components/layout/Header';
import { Button } from 'antd';
import { loginRequest } from './auth/auth.config';
import { JobProvider } from './contexts/JobContext';
import { NavigationProvider } from './contexts/NavigationContext';
import { CandidateDetailPage } from './pages/CandidateDetailPage';
import { LocalAuthWrapper } from './components/auth/LocalAuthWrapper';

const { Content } = Layout;

interface AppProps {
  instance: IPublicClientApplication;
}

const MainContent: React.FC = () => {
  const { instance } = useMsal();

  const handleRedirect = () => {
    instance
      .loginRedirect({
        ...loginRequest,
        prompt: 'create'
      })
      .catch(console.error);
  };

  return (
    <div className='App'>
      <LocalAuthWrapper>
        <NavigationProvider>
          <Layout style={{ minHeight: '100vh' }}>
            <Header />
            <JobProvider>
              <Content style={{ padding: '12px 48px', background: '#F5F5F5' }}>
                <Routes>
                  <Route path='/' element={<JobOrders />} />
                  <Route path='/job/:id/*' element={<JobDetails />} />
                  <Route path='/candidates' element={<CandidatesTable />} />
                  <Route path='/candidates/:id' element={<CandidateDetailPage />} />
                </Routes>
              </Content>
            </JobProvider>
          </Layout>
        </NavigationProvider>
      </LocalAuthWrapper>
    </div>
  );
};

export const App: React.FC<AppProps> = ({ instance }) => {
  return (
    <MsalProvider instance={instance}>
      <MainContent />
    </MsalProvider>
  );
};
