import React from 'react';
import { Breadcrumb } from 'antd';
import { Link, useLocation, useParams } from 'react-router-dom';
import { useJobContext } from '../../contexts/JobContext';
import { useFetch } from '../../hooks/useFetch';
import { endpoints } from '../../utils/api';
import type { Candidate } from '../../types/candidate';

export const Breadcrumbs: React.FC = () => {
  const location = useLocation();
  const { id } = useParams<{ id: string }>();
  const { jobData } = useJobContext();

  // Fetch candidate data if we're on a candidate detail page
  const shouldFetchCandidate = location.pathname.startsWith('/candidates/') && id;
  const { data: candidateData } = useFetch<Candidate>({
    url: shouldFetchCandidate ? endpoints.candidates.get(id) : '',
    skip: !shouldFetchCandidate,
    dependencies: [id, location.pathname]
  });

  // Don't render breadcrumbs on root pages
  if (location.pathname === '/' || location.pathname === '/candidates') {
    return null;
  }

  // Handle candidate detail pages
  if (location.pathname.startsWith('/candidates/') && id) {
    const candidateName = candidateData?.candidate_info?.personal_info?.full_name || 'Candidate Detail';

    const candidateItems = [
      {
        title: <Link to='/candidates'>Candidates</Link>
      },
      {
        title: candidateName
      }
    ];

    return <Breadcrumb style={{ marginBottom: 0 }} items={candidateItems} />;
  }

  // Handle job detail pages
  const jobName =
    (jobData?.position_info?.roleName || jobData?.position_info?.positionName) ?? 'Position/Role name not found';

  const jobItems = [
    {
      title: <Link to='/'>Job Orders</Link>
    },
    {
      title: jobName
    }
  ];

  return <Breadcrumb style={{ marginBottom: 0 }} items={jobItems} />;
};
