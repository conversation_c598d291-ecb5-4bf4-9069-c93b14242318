.site-description-item-profile-p-label{
    font-weight: bold;
}

/* Header styles */
.ant-layout-header {
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    z-index: 10;
}

/* Card styles */
.ant-card-head-title {
    font-weight: 600;
}

/* Table styles */
.ant-table-tbody > tr:hover > td {
    background-color: rgba(22, 119, 255, 0.05);
}



/* Ensure content area takes full height */
.ant-layout-content {
    min-height: calc(100vh - 64px);
}

/* Style for job description page */
.job-description h1, .job-description h2, .job-description h3, .job-description h4 {
    margin-bottom: 16px;
}

.benefit-icon {
    font-size: 24px;
    margin-right: 12px;
}

.breadcrumb-active {
    color: rgba(0, 0, 0, 0.45);
}

/* Button styles */
.ant-btn-primary {
    background-color: var(--primary-color);
}

/* Global styles for candidates actions dropdown - Modern Ant Design Menu - Figma specs */
.candidates-actions-menu {
    border-radius: 4px !important;
    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.15) !important;
    padding: 0 !important;
    width: 256px !important;
    min-width: 256px !important;
    border: none !important;
    background-color: #FFFFFF !important;
}

.candidates-actions-menu .ant-dropdown-menu-item {
    padding: 0 24px !important;
    margin: 0 !important;
    border-radius: 0 !important;
    transition: all 0.2s ease !important;
    border: none !important;
    position: relative !important;
    font-weight: 500 !important;
    background-color: transparent !important;
    height: 40px !important;
    line-height: 40px !important;
    display: flex !important;
    align-items: center !important;
}

/* Default state - black text and icons */
.candidates-actions-menu .ant-dropdown-menu-item,
.candidates-actions-menu .ant-dropdown-menu-item .action-item,
.candidates-actions-menu .ant-dropdown-menu-item .anticon,
.candidates-actions-menu .ant-dropdown-menu-item span {
    color: #000000 !important;
}

/* Hover state - purple background (Figma: #E6EFFF) */
.candidates-actions-menu .ant-dropdown-menu-item:hover,
.candidates-actions-menu .ant-dropdown-menu-item-selected {
    background-color: #E6EFFF !important;
}

/* Hover state - purple text and icons (Figma: #6C52FF) */
.candidates-actions-menu .ant-dropdown-menu-item:hover,
.candidates-actions-menu .ant-dropdown-menu-item:hover .action-item,
.candidates-actions-menu .ant-dropdown-menu-item:hover .anticon,
.candidates-actions-menu .ant-dropdown-menu-item:hover span,
.candidates-actions-menu .ant-dropdown-menu-item-selected,
.candidates-actions-menu .ant-dropdown-menu-item-selected .action-item,
.candidates-actions-menu .ant-dropdown-menu-item-selected .anticon,
.candidates-actions-menu .ant-dropdown-menu-item-selected span {
    color: #6C52FF !important;
}

/* Purple right border on hover (Figma: 3px Right side #6C52FF) */
.candidates-actions-menu .ant-dropdown-menu-item:hover,
.candidates-actions-menu .ant-dropdown-menu-item-selected {
    border-right: 3px solid #6C52FF !important;
}

.candidates-actions-menu .ant-dropdown-menu-item .action-item {
    display: inline-flex !important;
    align-items: center !important;
    gap: 10px !important;
    font-weight: 500 !important;
    white-space: nowrap !important;
}

.candidates-actions-menu .ant-dropdown-menu-item .action-icon {
    font-size: 16px !important;
}

/* Candidate Detail Tabs - Pill Style with Purple Theme */
.candidate-detail-tabs .ant-tabs-nav {
  margin-bottom: 24px !important;
}

.candidate-detail-tabs .ant-tabs-nav-list {
  gap: 8px !important;
}

.candidate-detail-tabs .ant-tabs-tab {
  border-radius: 20px !important;
  border: 1px solid #e8e8e8 !important;
  background: #ffffff !important;
  margin: 0 !important;
  padding: 8px 20px !important;
  transition: all 0.3s ease !important;
}

.candidate-detail-tabs .ant-tabs-tab:hover {
  border-color: #6C52FF !important;
  color: #6C52FF !important;
}

.candidate-detail-tabs .ant-tabs-tab-active {
  background: #E6EFFF !important;
  border: none !important;
  color: #7B66FF !important;
}

.candidate-detail-tabs .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #7B66FF !important;
}

.candidate-detail-tabs .ant-tabs-tab .ant-tabs-tab-btn {
  color: #666666;
  font-weight: 500;
}

.candidate-detail-tabs .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #7B66FF !important;
  font-weight: 600;
}

.candidate-detail-tabs .ant-tabs-ink-bar {
  display: none !important;
}

.candidate-detail-tabs .ant-tabs-content-holder {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* Upload Modal Styling */
.ant-upload-drag-icon {
    text-align: center !important;
}

.ant-upload-drag-icon .anticon {
    font-size: 48px !important;
    color: #1890ff !important;
    margin-bottom: 16px !important;
}

.ant-upload-text {
    font-size: 16px !important;
    font-weight: 500 !important;
    color: #262626 !important;
    margin: 0 0 8px 0 !important;
    text-align: center !important;
}

.ant-upload-hint {
    font-size: 14px !important;
    color: #8c8c8c !important;
    margin: 0 !important;
    text-align: center !important;
    line-height: 1.6 !important;
}

/* Upload button styling */
.ant-btn-primary.ant-btn-loading,
.ant-btn-primary:not(:disabled) {
    color: #FFFFFF !important;
}

/* Cancel button styling - purple border */
.ant-modal .ant-btn:not(.ant-btn-primary) {
    border-color: #7B66FF !important;
    color: #7B66FF !important;
}

.ant-modal .ant-btn:not(.ant-btn-primary):hover {
    border-color: #6C52FF !important;
    color: #6C52FF !important;
}

