import React from 'react';
import { Row, Col, Tag } from 'antd';
import { Candidate } from '../../types/candidate';
import ProgressCircle from '../common/ProgressCircle';
import { getResumePercentage, getTechInterviewPercentage } from '../../utils/candidateUtils';
import { ANALYSIS_SECTION_STYLES, COLORS, SIZES } from '../../utils/candidateStyles';

interface AnalysisSectionProps {
  candidate: Candidate;
}

export const AnalysisSection: React.FC<AnalysisSectionProps> = ({ candidate }) => {
  const resumePercentage = getResumePercentage(candidate);
  const techInterviewPercentage = getTechInterviewPercentage(candidate);

  return (
    <div style={ANALYSIS_SECTION_STYLES.container}>
      <Row gutter={16} align='middle'>
        <Col span={6}>
          <div style={ANALYSIS_SECTION_STYLES.progressContainer}>
            <ProgressCircle
              percent={resumePercentage}
              size={SIZES.PROGRESS_MEDIUM}
              strokeColor={COLORS.PROGRESS_BLUE}
              strokeWidth={4}
              fontSize={14}
              fontWeight={900}
              textColor='#141414'
              label='RESUME'
              labelStyle={ANALYSIS_SECTION_STYLES.progressLabel}
            />
            <ProgressCircle
              percent={techInterviewPercentage}
              size={SIZES.PROGRESS_MEDIUM}
              strokeColor={COLORS.PROGRESS_BLUE}
              strokeWidth={4}
              fontSize={14}
              fontWeight={900}
              textColor='#141414'
              label='TECH INTERVIEW'
              labelStyle={{ ...ANALYSIS_SECTION_STYLES.progressLabel, whiteSpace: 'nowrap' }}
            />
          </div>
        </Col>
        <Col span={18}>
          <div style={ANALYSIS_SECTION_STYLES.feedbackSection}>
            <div style={ANALYSIS_SECTION_STYLES.feedbackTitle}>Technical Interview Summary:</div>
            <div style={ANALYSIS_SECTION_STYLES.feedbackText}>
              {candidate.interview_data?.explanation || 'No technical interview feedback has been entered yet.'}
            </div>

            {candidate.interview_data?.overall_seniority && (
              <div>
                <Tag color={'cyan'} style={{ fontWeight: 600, fontSize: 14, padding: '2px 12px' }}>
                  {candidate.interview_data?.overall_seniority?.toUpperCase() || ''}
                </Tag>
              </div>
            )}
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default AnalysisSection;
