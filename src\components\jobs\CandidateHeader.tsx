import React from 'react';
import { Row, Col, Typography } from 'antd';
import { Candidate } from '../../types/candidate';
import { getCandidateInitials } from '../../utils/candidateUtils';
import { COLORS, SIZES } from '../../utils/candidateStyles';

interface CandidateHeaderProps {
  candidate: Candidate;
}

export const CandidateHeader: React.FC<CandidateHeaderProps> = ({ candidate }) => {
  const initials = getCandidateInitials(candidate.candidate_info.full_name);

  return (
    <Row align='middle' gutter={24} style={{ marginBottom: 24, flexWrap: 'wrap' }}>
      <Col>
        <div
          style={{
            width: SIZES.AVATAR_LARGE,
            height: SIZES.AVATAR_LARGE,
            borderRadius: '50%',
            background: '#6c63ff',
            color: '#fff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: 28,
            fontWeight: 600
          }}
        >
          {initials}
        </div>
      </Col>
      <Col flex='auto'>
        <Typography.Title level={4} style={{ margin: 0 }}>
          {candidate.candidate_info?.full_name}
        </Typography.Title>
      </Col>
    </Row>
  );
};

export default CandidateHeader;