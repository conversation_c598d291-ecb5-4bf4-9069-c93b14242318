import React, { useMemo } from 'react';
import { <PERSON><PERSON>, Badge } from 'antd';
import { FilterOutlined } from '@ant-design/icons';
import { FilterPopover } from './FilterPopover';
import type { CandidateFilter, JobFilter, FilterOption, FilterConfiguration } from '../../types/filters';

interface TableFilterControllerProps {
  filterType: 'candidate' | 'job';
  filters: CandidateFilter | JobFilter;
  onFiltersChange: (filters: CandidateFilter | JobFilter) => void;
  onClearFilters: () => void;
  onApplyFilters: () => void;
  availableOptions?: {
    roles?: FilterOption[];
    countries?: FilterOption[];
    createdBy?: FilterOption[];
    clients?: FilterOption[];
    locations?: FilterOption[];
  };
  loading?: boolean;
  filterConfig?: FilterConfiguration;
}

export const TableFilterController: React.FC<TableFilterControllerProps> = ({
  filterType,
  filters,
  onFiltersChange,
  onClearFilters,
  onApplyFilters,
  availableOptions = {},
  loading = false,
  filterConfig = {}
}) => {
  // Count active filters (excluding searchTerm since it's outside the popup)
  const activeFilterCount = useMemo(() => {
    let count = 0;
    
    // Count base filters
    if (filters.createdFrom) count++;
    if (filters.createdTo) count++;
    
    // Count specific filters based on type (only count visible filters)
    if (filterType === 'candidate') {
      const candidateFilters = filters as CandidateFilter;
      if ((filterConfig.status?.visible !== false) && candidateFilters.status && candidateFilters.status !== 'all') count++;
      if ((filterConfig.role?.visible !== false) && candidateFilters.role) count++;
      if ((filterConfig.country?.visible !== false) && candidateFilters.country) count++;
      if ((filterConfig.createdBy?.visible !== false) && candidateFilters.createdBy) count++;
    } else {
      const jobFilters = filters as JobFilter;
      if ((filterConfig.clientName?.visible !== false) && jobFilters.clientName) count++;
      if ((filterConfig.location?.visible !== false) && jobFilters.location) count++;
      if ((filterConfig.stage?.visible !== false) && jobFilters.stage && jobFilters.stage !== 'all') count++;
    }
    
    return count;
  }, [filters, filterType]);

  return (
    <FilterPopover
      filterType={filterType}
      filters={filters}
      onFiltersChange={onFiltersChange}
      onApplyFilters={onApplyFilters}
      onClearFilters={onClearFilters}
      availableOptions={availableOptions}
      loading={loading}
      filterConfig={filterConfig}
    >
      <Badge count={activeFilterCount} offset={[-8, 8]}>
        <Button
          icon={<FilterOutlined />}
          style={{
            display: 'flex',
            alignItems: 'center',
            height: '32px',
            borderRadius: '10px',
            border: activeFilterCount > 0 ? '1px solid #7B66FF' : '1px solid #d9d9d9',
            background: 'transparent',
            color: activeFilterCount > 0 ? '#7B66FF' : '#666'
          }}
        />
      </Badge>
    </FilterPopover>
  );
}; 