import React from 'react';
import { Layout, Dropdown, Space, Button } from 'antd';
import { DownOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import { Navbar } from './Navbar';
import { useMsal } from '@azure/msal-react';
import type { MenuProps } from 'antd';
import { useLocation } from 'react-router-dom';
import { useNavigation } from '../../contexts/NavigationContext';

const { Header: AntHeader } = Layout;

export const Header: React.FC = () => {
  const { instance } = useMsal();
  const activeAccount = instance.getActiveAccount();
  const location = useLocation();
  const { navigateBack } = useNavigation();

  const handleSignOut = () => {
    instance.logoutRedirect({
      account: instance.getActiveAccount(),
      postLogoutRedirectUri: 'http://localhost:5173/'
    });
  };

  // Don't show header back button on detail pages - they have their own back buttons
  const showBackButton = false;

  const items: MenuProps['items'] = [
    {
      key: 'signout',
      label: <a onClick={handleSignOut}>Sign Out</a>
    }
  ];

  return (
    <AntHeader
      style={{
        background: '#fff',
        borderBottom: '1px solid #f0f0f0',
        padding: '16px 32px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {showBackButton && (
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={navigateBack}
            style={{
              marginRight: '16px',
              display: 'flex',
              alignItems: 'center',
              padding: '8px 12px',
              fontSize: '14px',
              fontWeight: '500',
              color: '#6366f1',
              background: 'transparent',
              border: 'none',
              cursor: 'pointer',
              borderRadius: '6px',
              transition: 'all 0.2s'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = '#f8f9ff';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'transparent';
            }}
          >
            Back
          </Button>
        )}
        <img
          src='https://static.wixstatic.com/media/95b711_5d533c13c70f4de79d807c4e13629e98~mv2.png/v1/fit/w_2500,h_1330,al_c/95b711_5d533c13c70f4de79d807c4e13629e98~mv2.png'
          alt='Logo'
          style={{ height: '40px', marginRight: '16px' }}
        />
      </div>
      <div style={{ display: 'flex', alignItems: 'center', gap: '24px' }}>
        <Navbar />
        <Dropdown menu={{ items }}>
          <a onClick={(e) => e.preventDefault()}>
            <Space>
              {activeAccount?.name}
              <DownOutlined />
            </Space>
          </a>
        </Dropdown>
      </div>
    </AntHeader>
  );
};
