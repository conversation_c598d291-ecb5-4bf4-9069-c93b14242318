/* CSS Custom Properties for responsive table - Proportional scaling */
:root {
    /* Base widths for large screens - maintaining proportions */
    --table-name-width: 300px;      /* 17.2% */
    --table-role-width: 340px;      /* 19.5% */
    --table-email-width: 260px;     /* 14.9% */
    --table-status-width: 120px;    /* 6.9% */
    --table-country-width: 210px;   /* 12.1% */
    --table-date-width: 170px;      /* 9.8% */
    --table-created-by-width: 170px; /* 9.8% */
    --table-updated-by-width: 170px; /* 9.8% */
}

/* Responsive table column widths */
.candidates-table {
    width: 100%;
    table-layout: fixed;
}

.candidates-table .role-column {
    width: var(--table-role-width);
    min-width: var(--table-role-width);
    max-width: var(--table-role-width);
}

.candidates-table .email-column {
    width: var(--table-email-width);
    min-width: var(--table-email-width);
    max-width: var(--table-email-width);
}

.candidates-table .name-column {
    width: var(--table-name-width);
    min-width: var(--table-name-width);
    max-width: var(--table-name-width);
}

.candidates-table .status-column {
    width: var(--table-status-width);
    min-width: var(--table-status-width);
    max-width: var(--table-status-width);
}

.candidates-table .country-column {
    width: var(--table-country-width);
    min-width: var(--table-country-width);
    max-width: var(--table-country-width);
}

.candidates-table .date-column {
    width: var(--table-date-width);
    min-width: var(--table-date-width);
    max-width: var(--table-date-width);
}

.candidates-table .created-by-column {
    width: var(--table-created-by-width);
    min-width: var(--table-created-by-width);
    max-width: var(--table-created-by-width);
}

.candidates-table .updated-by-column {
    width: var(--table-updated-by-width);
    min-width: var(--table-updated-by-width);
    max-width: var(--table-updated-by-width);
}

/* Responsive content containers */
.table-cell-content {
    display: flex;
    align-items: center;
    width: 100%;
    min-width: 0; /* Important for flex text truncation */
}

.table-text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    min-width: 0;
}

/* Candidates search row alignment */
.candidates-search-row {
    padding: 7px !important;
}

/* Search input styling to match mockup */
.candidates-search-row .ant-input-search {
    /* height: 40px; */
}

.candidates-search-row .ant-input-search .ant-input {
    /* height: 40px; */
    border-radius: 6px 0 0 6px;
}

.candidates-search-row .ant-input-search .ant-input-search-button {
    /* height: 40px; */
    background-color: #7B66FF !important;
    border-color: #7B66FF !important;
    color: #fff !important;
    border-radius: 0 6px 6px 0;
}

.candidates-search-row .ant-input-search .ant-input-search-button:hover {
    background-color: #6952e6 !important;
    border-color: #6952e6 !important;
    color: #fff !important;
}

/* Ensure New Candidate button matches search input height */
.candidates-search-row .ant-btn {
    /* height: 40px; */
    display: flex;
    align-items: center;
    border-radius: 6px;
}

/* Align with Ant Design table content area */
.candidates-table.ant-table-wrapper {
    margin: 0 !important;
}

.candidates-table .ant-table {
    margin: 0 !important;
}

.candidates-table .ant-table-container {
    position: relative;
    z-index: 1;
}

.candidates-table .ant-table-body {
    position: relative;
    z-index: 1;
}

/* Actions dropdown styling */
.candidates-actions-dropdown .ant-dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 8px;
    min-width: 180px;
}

.candidates-actions-dropdown .ant-dropdown-menu-item {
    padding: 12px 16px !important;
    margin: 2px 0 !important;
    border-radius: 6px !important;
    transition: all 0.2s ease !important;
    border: none !important;
    position: relative;
    font-weight: 500 !important;
    background-color: transparent !important;
}

/* Default state - black text and icons */
.candidates-actions-dropdown .ant-dropdown-menu-item .ant-dropdown-menu-title-content,
.candidates-actions-dropdown .ant-dropdown-menu-item .action-item,
.candidates-actions-dropdown .ant-dropdown-menu-item .action-item *,
.candidates-actions-dropdown .ant-dropdown-menu-item .action-icon,
.candidates-actions-dropdown .ant-dropdown-menu-item .anticon {
    color: #000000 !important;
}

/* Hover state - purple background and accent line */
.candidates-actions-dropdown .ant-dropdown-menu-item:hover {
    background-color: #E6EFFF !important;
}

/* Hover state - purple text and icons */
.candidates-actions-dropdown .ant-dropdown-menu-item:hover .ant-dropdown-menu-title-content,
.candidates-actions-dropdown .ant-dropdown-menu-item:hover .action-item,
.candidates-actions-dropdown .ant-dropdown-menu-item:hover .action-item *,
.candidates-actions-dropdown .ant-dropdown-menu-item:hover .action-icon,
.candidates-actions-dropdown .ant-dropdown-menu-item:hover .anticon,
.candidates-actions-dropdown .ant-dropdown-menu-item:hover span,
.candidates-actions-dropdown .ant-dropdown-menu-item:hover span.action-item,
.candidates-actions-dropdown .ant-dropdown-menu-item:hover span.action-icon,
.candidates-actions-dropdown .ant-dropdown-menu-item:hover .anticon-cloud-download,
.candidates-actions-dropdown .ant-dropdown-menu-item:hover .anticon-stop,
.candidates-actions-dropdown .ant-dropdown-menu-item:hover .anticon-play-circle,
.candidates-actions-dropdown .ant-dropdown-menu-item:hover .anticon-delete {
    color: #6C52FF !important;
}

/* Purple left accent line on hover */
.candidates-actions-dropdown .ant-dropdown-menu-item:hover::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background-color: #6C52FF;
    border-radius: 0 3px 3px 0;
}

/* More aggressive targeting for hover text colors */
.ant-dropdown-menu.candidates-actions-dropdown .ant-dropdown-menu-item:hover,
.ant-dropdown-menu.candidates-actions-dropdown .ant-dropdown-menu-item:hover * {
    color: #6C52FF !important;
}

/* Global override for this specific dropdown */
div[data-menu-list="true"] .ant-dropdown-menu-item:hover .action-item,
div[data-menu-list="true"] .ant-dropdown-menu-item:hover .action-item *,
div[data-menu-list="true"] .ant-dropdown-menu-item:hover [class*="anticon"] {
    color: #6C52FF !important;
}

.candidates-actions-dropdown .ant-dropdown-menu-item .action-item {
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    font-weight: 500 !important;
    color: #000000 !important;
    white-space: nowrap !important;
}

.candidates-actions-dropdown .ant-dropdown-menu-item .action-icon {
    color: #000000 !important;
    font-size: 16px !important;
}



/* Actions column hover effect */
.candidates-table .ant-table-tbody .ant-table-row:hover .anticon-more {
    background-color: #f0f0f0;
    border-radius: 4px;
}

/* Ensure dropdown doesn't clip at bottom of screen */
.candidates-actions-dropdown {
    z-index: 1050 !important;
}

/* Fix dropdown appearing behind table columns */
.candidates-table .ant-table-tbody .ant-table-cell {
    position: relative;
    z-index: 1;
}

.candidates-table .ant-table-tbody .ant-table-cell:last-child {
    z-index: 10;
}

.ant-dropdown {
    z-index: 1060 !important;
}

.ant-dropdown-menu {
    z-index: 1060 !important;
}

/* Ensure actions column has proper stacking context */
.candidates-table .ant-table-cell[data-testid="actions-cell"] {
    z-index: 10 !important;
    position: relative;
}

/* More specific targeting for the actions dropdown */
.candidates-actions-dropdown .ant-dropdown-trigger {
    z-index: 10;
    position: relative;
}

.candidates-actions-dropdown[data-menu-list="true"] {
    z-index: 1070 !important;
}

/* Media queries for responsive design - Proportional scaling */
@media (max-width: 1400px) {
    :root {
        /* Scale down by ~15% while maintaining proportions */
        --table-name-width: 255px;      /* 17.2% */
        --table-role-width: 290px;      /* 19.5% */
        --table-email-width: 220px;     /* 14.9% */
        --table-status-width: 100px;    /* 6.9% */
        --table-country-width: 180px;   /* 12.1% */
        --table-date-width: 145px;      /* 9.8% */
        --table-created-by-width: 145px; /* 9.8% */
        --table-updated-by-width: 145px; /* 9.8% */
    }
}

@media (max-width: 1200px) {
    :root {
        /* Scale down by ~25% while maintaining proportions */
        --table-name-width: 225px;      /* 17.2% */
        --table-role-width: 255px;      /* 19.5% */
        --table-email-width: 195px;     /* 14.9% */
        --table-status-width: 90px;     /* 6.9% */
        --table-country-width: 160px;   /* 12.1% */
        --table-date-width: 130px;      /* 9.8% */
        --table-created-by-width: 130px; /* 9.8% */
        --table-updated-by-width: 130px; /* 9.8% */
    }
}

@media (max-width: 992px) {
    :root {
        /* Scale down by ~35% while maintaining proportions */
        --table-name-width: 195px;      /* 17.2% */
        --table-role-width: 220px;      /* 19.5% */
        --table-email-width: 170px;     /* 14.9% */
        --table-status-width: 80px;     /* 6.9% */
        --table-country-width: 135px;   /* 12.1% */
        --table-date-width: 110px;      /* 9.8% */
        --table-created-by-width: 110px; /* 9.8% */
        --table-updated-by-width: 110px; /* 9.8% */
    }
}

@media (max-width: 768px) {
    :root {
        /* Scale down by ~45% while maintaining proportions */
        --table-name-width: 165px;      /* 17.2% */
        --table-role-width: 185px;      /* 19.5% */
        --table-email-width: 145px;     /* 14.9% */
        --table-status-width: 65px;     /* 6.9% */
        --table-country-width: 115px;   /* 12.1% */
        --table-date-width: 95px;       /* 9.8% */
        --table-created-by-width: 95px;  /* 9.8% */
        --table-updated-by-width: 95px;  /* 9.8% */
    }

    /* Candidates search row responsive styling */
    .candidates-search-row {
        flex-direction: column !important;
        align-items: stretch !important;
        gap: 12px !important;
        padding: 0 16px !important;
    }
    
    .candidates-search-row > div:first-child {
        order: 1;
        text-align: left !important;
    }
    
    .candidates-search-row > div:last-child {
        order: 2;
        flex-direction: column !important;
        gap: 8px !important;
    }
    
    .candidates-search-row .ant-input-search {
        width: 100% !important;
        max-width: 100% !important;
    }
}

@media (max-width: 480px) {
    :root {
        /* Further scale down for mobile - 55% reduction */
        --table-name-width: 135px;      /* 17.2% */
        --table-role-width: 155px;      /* 19.5% */
        --table-email-width: 120px;     /* 14.9% */
        --table-status-width: 55px;     /* 6.9% */
        --table-country-width: 95px;    /* 12.1% */
        --table-date-width: 80px;       /* 9.8% */
        --table-created-by-width: 80px;  /* 9.8% */
        --table-updated-by-width: 80px;  /* 9.8% */
    }

    .candidates-search-row {
        gap: 8px !important;
    }
} 