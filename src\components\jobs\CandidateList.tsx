import React, { useEffect, useState } from 'react';
import { Spin, Drawer, Form, Row, Col, Input, DatePicker, Select, Button, Typography, Tag, message, Card } from 'antd';
import {
  UserOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  CommentOutlined,
  TranslationOutlined,
  TransactionOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { api, endpoints } from '../../utils/api';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { Candidate } from '../../types/candidate';
import CandidateCard from './CandidateCard';
import AnalysisSection from './AnalysisSection';
import NavigationPills from './NavigationPills';
import CandidateHeader from './CandidateHeader';
import useInterviewFeedback from '../../hooks/useInterviewFeedback';
import {
  getStatusColor,
  getRecommendationColor,
  getRecommendationText,
  extractFeedbackComments
} from '../../utils/candidateUtils';
import { HEADER_STYLES } from '../../utils/candidateStyles';

interface CandidateListProps {
  positionId: string;
}

const { Text } = Typography;

const CandidateList: React.FC<CandidateListProps> = ({ positionId }) => {
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [selectedCandidate, setSelectedCandidate] = useState<Candidate | null>(null);
  const [activeTab, setActiveTab] = useState('hr');
  const [submittingHR, setSubmittingHR] = useState<boolean>(false);
  const [submittingTech, setSubmittingTech] = useState<boolean>(false);

  // Use custom hook for form management
  const { submitHRFeedback, submitTechFeedback } = useInterviewFeedback(positionId);

  const [hrForm] = Form.useForm();
  const [techForm] = Form.useForm();
  const fetchCandidates = async () => {
    try {
      const response = await api.get(endpoints.interview.getByPositionId(positionId));

      // Add mock match percentage for demo
      const candidatesWithMatch = response.data.map((candidate: Candidate, index: number) => {
        return {
          ...candidate
        };
      });

      setCandidates(candidatesWithMatch);
    } catch (error) {
      message.error('Failed to load candidates.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCandidates();
  }, [positionId]);

  // Helper function to update forms with candidate data
  const updateFormsWithCandidateData = (candidate: Candidate) => {
    hrForm.setFieldsValue({
      hrRecruiter: candidate.recruiter_hr_id || '',
      hrInterviewDate: candidate.interview_date_hr ? dayjs(candidate.interview_date_hr) : null,
      hrFeedbackDate: candidate.feedback_date_hr ? dayjs(candidate.feedback_date_hr) : null,
      hrScheduledBy: candidate.scheduled_hr_id || '',
      hrStatus: candidate.status_hr || 'not_scheduled',
      hrRecommendation:
        candidate.recommendation_hr !== null && candidate.recommendation_hr !== undefined
          ? candidate.recommendation_hr
            ? 'I recommend continuing the process'
            : 'I do not recommend'
          : undefined,
      hrComments: extractFeedbackComments(candidate.feedback_hr),
      hrTranscript: candidate.transcript_hr || ''
    });

    techForm.setFieldsValue({
      techRecruiter: candidate.recruiter_tec_id || '',
      techInterviewDate: candidate.interview_date_tec ? dayjs(candidate.interview_date_tec) : null,
      techFeedbackDate: candidate.feedback_date_tec ? dayjs(candidate.feedback_date_tec) : null,
      techScheduledBy: candidate.scheduled_tec_id || '',
      techStatus: candidate.status_tec || 'not_scheduled',
      techRecommendation:
        candidate.recommendation_tec !== null && candidate.recommendation_tec !== undefined
          ? candidate.recommendation_tec
            ? 'I recommend continuing the process'
            : 'I do not recommend'
          : undefined,
      techComments: extractFeedbackComments(candidate.feedback_tec),
      techTranscript: candidate.transcript_tec || ''
    });
  };

  const handleCandidateClick = (candidate: Candidate) => {
    setSelectedCandidate(candidate);
    setActiveTab('hr');
    updateFormsWithCandidateData(candidate);
  };

  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  const handleSubmitHRFeedback = async (values: any) => {
    if (selectedCandidate) {
      setSubmittingHR(true);
      try {
        await submitHRFeedback(selectedCandidate, values, (updatedCandidate: Candidate) => {
          // Update the candidate in the list
          setCandidates((prevCandidates) =>
            prevCandidates.map((candidate) =>
              candidate.candidate_id === updatedCandidate.candidate_id ? updatedCandidate : candidate
            )
          );
          // Update the selected candidate with the new data
          setSelectedCandidate(updatedCandidate);
          // Update forms with the fresh saved data
          updateFormsWithCandidateData(updatedCandidate);
        });
      } finally {
        setSubmittingHR(false);
        fetchCandidates();
        setSelectedCandidate(null);
      }
    }
  };

  const handleSubmitTechFeedback = async (values: any) => {
    if (selectedCandidate) {
      setSubmittingTech(true);
      try {
        await submitTechFeedback(selectedCandidate, values, (updatedCandidate: Candidate) => {
          // Update the candidate in the list
          setCandidates((prevCandidates) =>
            prevCandidates.map((candidate) =>
              candidate.candidate_id === updatedCandidate.candidate_id ? updatedCandidate : candidate
            )
          );
          // Update the selected candidate with the new data
          setSelectedCandidate(updatedCandidate);
          // Update forms with the fresh saved data
          updateFormsWithCandidateData(updatedCandidate);
        });
      } finally {
        setSubmittingTech(false);
        fetchCandidates();
        setSelectedCandidate(null);
      }
    }
  };

  const renderHRFeedbackContent = () => {
    if (!selectedCandidate) return null;

    const isHRCompleted = selectedCandidate.status_hr === 'completed';

    const statusColor = getStatusColor(selectedCandidate.status_hr);
    const recColor = getRecommendationColor(selectedCandidate.recommendation_hr);
    const statusText = typeof selectedCandidate.status_hr === 'string' ? selectedCandidate.status_hr : 'N/A';
    const recommendationText = getRecommendationText(selectedCandidate.recommendation_hr);

    if (isHRCompleted) {
      return (
        <div className='feedback-read-only'>
          <Row gutter={16} style={{ marginBottom: 16, flexWrap: 'wrap' }}>
            <Col span={6}>
              <Text type='secondary'>
                <UserOutlined /> Recruiter
              </Text>
              <div style={{ fontWeight: 500 }}>{selectedCandidate.recruiter_hr_id || 'N/A'}</div>
            </Col>
            <Col span={6}>
              <Text type='secondary'>
                <CalendarOutlined /> Interview Date
              </Text>
              <div style={{ fontWeight: 500 }}>
                {selectedCandidate.interview_date_hr
                  ? dayjs(selectedCandidate.interview_date_hr).format('YYYY-MM-DD HH:mm')
                  : 'N/A'}
              </div>
            </Col>
            <Col span={6}>
              <Text type='secondary'>
                <UserOutlined /> Scheduled By
              </Text>
              <div style={{ fontWeight: 500 }}>{selectedCandidate.scheduled_hr_id || 'N/A'}</div>
            </Col>
          </Row>

          <Row gutter={16} style={{ marginBottom: 16, flexWrap: 'wrap' }}>
            <Col span={6}>
              <Text type='secondary'>
                <CalendarOutlined /> Feedback Date
              </Text>
              <div style={{ fontWeight: 500 }}>{selectedCandidate.feedback_date_hr?.substr(0, 10) || 'N/A'}</div>
            </Col>
            <Col span={6}>
              <Text type='secondary'>
                <CheckCircleOutlined /> Status
              </Text>
              <div>
                <Tag color={statusColor} style={{ fontWeight: 600, fontSize: 14, padding: '2px 12px' }}>
                  {statusText}
                </Tag>
              </div>
            </Col>
            <Col span={6}>
              <Text type='secondary'>
                <CheckCircleOutlined /> Recommendation
              </Text>
              <div>
                <Tag color={recColor} style={{ fontWeight: 600, fontSize: 14, padding: '2px 12px' }}>
                  {recommendationText}
                </Tag>
              </div>
            </Col>
          </Row>

          <div
            style={{
              background: '#f6f7fb',
              borderRadius: 8,
              padding: 16,
              marginBottom: 16,
              border: '1px solid #e6e6e6',
              wordBreak: 'break-word',
              maxWidth: '100%'
            }}
          >
            <Text strong style={{ color: '#6c63ff' }}>
              <CommentOutlined /> Comments
            </Text>
            <div style={{ maxHeight: 80, overflowY: 'auto', marginTop: 8, color: '#222', fontSize: 15 }}>
              {typeof selectedCandidate.feedback_hr?.comments === 'string'
                ? selectedCandidate.feedback_hr?.comments
                : 'N/A'}
            </div>
          </div>
          <div
            style={{
              background: '#f6f7fb',
              borderRadius: 8,
              padding: 16,
              border: '1px solid #e6e6e6',
              wordBreak: 'break-word',
              maxWidth: '100%'
            }}
          >
            <Text strong style={{ color: '#6c63ff' }}>
              <FileTextOutlined /> Transcript
            </Text>
            <div
              style={{
                maxHeight: 120,
                overflowY: 'auto',
                marginTop: 8,
                whiteSpace: 'pre-line',
                color: '#222',
                fontSize: 15
              }}
            >
              {selectedCandidate.transcript_hr ? selectedCandidate.transcript_hr : 'N/A'}
            </div>
          </div>
        </div>
      );
    }

    return (
      <Form form={hrForm} layout='vertical' onFinish={handleSubmitHRFeedback}>
        {/* HR form fields */}
        <Row gutter={16}>
          <Col span={8}>
            <Text type='secondary'>
              <UserOutlined /> Recruiter
            </Text>
            <Form.Item name='hrRecruiter' rules={[{ required: true, message: 'Please input the recruiter name!' }]}>
              <Input placeholder='Enter Recruiter Name' />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Text type='secondary'>
              <CalendarOutlined /> Interview Date
            </Text>
            <Form.Item
              name='hrInterviewDate'
              rules={[{ required: true, message: 'Please select the interview date!' }]}
            >
              <DatePicker showTime style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Text type='secondary'>
              <CalendarOutlined /> Feedback Date
            </Text>
            <Form.Item
              name='hrFeedbackDate'
              dependencies={['hrInterviewDate']}
              rules={[
                { required: false, message: 'Please select the feedback date!' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || !getFieldValue('hrInterviewDate')) {
                      return Promise.resolve();
                    }
                    if (value.isAfter(getFieldValue('hrInterviewDate'))) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('Feedback date must be after interview date!'));
                  }
                })
              ]}
            >
              <DatePicker showTime style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={8}>
            <Text type='secondary'>
              <UserOutlined /> Scheduled By
            </Text>
            <Form.Item
              name='hrScheduledBy'
              rules={[{ required: false, message: 'Please input who scheduled the interview!' }]}
            >
              <Input placeholder='Enter Scheduler Name' />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Text type='secondary'>
              <CheckCircleOutlined /> Status
            </Text>
            <Form.Item name='hrStatus' rules={[{ required: false, message: 'Please select the status!' }]}>
              <Select placeholder='Select Status'>
                <Select.Option value='scheduled'>scheduled</Select.Option>
                <Select.Option value='in_progress'>in_progress</Select.Option>
                <Select.Option value='completed'>completed</Select.Option>
                <Select.Option value='cancelled'>cancelled</Select.Option>
                <Select.Option value='not_scheduled'>not_scheduled</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Text type='secondary'>
              <CheckCircleOutlined /> Recommendation
            </Text>
            <Form.Item name='hrRecommendation' rules={[{ required: true, message: 'Please select a recommendation!' }]}>
              <Select placeholder='Select Recommendation'>
                <Select.Option value='I recommend continuing the process'>
                  I recommend continuing the process
                </Select.Option>
                <Select.Option value='I do not recommend'>I do not recommend</Select.Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Text type='secondary'>
          <CommentOutlined /> HR Comments
        </Text>
        <Form.Item label='' name='hrComments'>
          <Input.TextArea rows={4} placeholder='Write here your note' />
        </Form.Item>

        <Text type='secondary'>
          <FileTextOutlined /> Transcript
        </Text>
        <Form.Item label='' name='hrTranscript'>
          <Input.TextArea rows={4} placeholder='Write here your note' />
        </Form.Item>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item>
              <Button type='primary' htmlType='submit' loading={submittingHR} disabled={submittingHR}>
                {submittingHR ? 'Saving...' : 'Save & send'}
              </Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    );
  };

  const renderTechFeedbackContent = () => {
    if (!selectedCandidate) return null;

    let statusColor = 'default';
    if (selectedCandidate.status_tec === 'Interview Conducted') statusColor = 'red';
    else if (selectedCandidate.status_tec === 'Scheduled') statusColor = 'blue';
    else if (selectedCandidate.status_tec === 'Pending') statusColor = 'orange';
    else if (selectedCandidate.status_tec) statusColor = 'geekblue';

    const isTechCompleted = selectedCandidate.status_tec === 'completed';

    const recColor = selectedCandidate.recommendation_tec ? 'cyan' : 'default';

    const statusText = typeof selectedCandidate.status_tec === 'string' ? selectedCandidate.status_tec : 'N/A';
    const recommendationText = selectedCandidate.recommendation_tec === true ? 'Recommended' : 'Not Recommended';

    if (isTechCompleted) {
      return (
        <div className='feedback-read-only'>
          <Row gutter={16} style={{ marginBottom: 16, flexWrap: 'wrap' }}>
            <Col span={6}>
              <Text type='secondary'>
                <UserOutlined /> Recruiter
              </Text>
              <div style={{ fontWeight: 500 }}>{selectedCandidate.recruiter_tec_id || 'N/A'}</div>
            </Col>
            <Col span={6}>
              <Text type='secondary'>
                <CalendarOutlined /> Interview Date
              </Text>
              <div style={{ fontWeight: 500 }}>
                {selectedCandidate.interview_date_tec
                  ? dayjs(selectedCandidate.interview_date_tec).format('YYYY-MM-DD HH:mm')
                  : 'N/A'}
              </div>
            </Col>
            <Col span={6}>
              <Text type='secondary'>
                <UserOutlined /> Scheduled By
              </Text>
              <div style={{ fontWeight: 500 }}>{selectedCandidate.scheduled_tec_id || 'N/A'}</div>
            </Col>
          </Row>

          <Row gutter={16} style={{ marginBottom: 16, flexWrap: 'wrap' }}>
            <Col span={6}>
              <Text type='secondary'>
                <CalendarOutlined /> Feedback Date
              </Text>
              <div style={{ fontWeight: 500 }}>{selectedCandidate.feedback_date_tec?.substr(0, 10) || 'N/A'}</div>
            </Col>
            <Col span={6}>
              <Text type='secondary'>
                <CheckCircleOutlined /> Status
              </Text>
              <div>
                <Tag color={statusColor} style={{ fontWeight: 600, fontSize: 14, padding: '2px 12px' }}>
                  {statusText}
                </Tag>
              </div>
            </Col>
            <Col span={6}>
              <Text type='secondary'>
                <CheckCircleOutlined /> Recommendation
              </Text>
              <div>
                <Tag color={recColor} style={{ fontWeight: 600, fontSize: 14, padding: '2px 12px' }}>
                  {recommendationText}
                </Tag>
              </div>
            </Col>
          </Row>

          <div
            style={{
              background: '#f6f7fb',
              borderRadius: 8,
              padding: 16,
              marginBottom: 16,
              border: '1px solid #e6e6e6',
              wordBreak: 'break-word',
              maxWidth: '100%'
            }}
          >
            <Text strong style={{ color: '#6c63ff' }}>
              <CommentOutlined /> Comments
            </Text>
            <div style={{ maxHeight: 80, overflowY: 'auto', marginTop: 8, color: '#222', fontSize: 15 }}>
              {typeof selectedCandidate.feedback_tec?.additionalProp1 === 'string'
                ? selectedCandidate.feedback_tec?.additionalProp1
                : 'N/A'}
            </div>
          </div>
          <div
            style={{
              background: '#f6f7fb',
              borderRadius: 8,
              padding: 16,
              border: '1px solid #e6e6e6',
              wordBreak: 'break-word',
              maxWidth: '100%'
            }}
          >
            <Text strong style={{ color: '#6c63ff' }}>
              <FileTextOutlined /> Transcript
            </Text>
            <div
              style={{
                maxHeight: 120,
                overflowY: 'auto',
                marginTop: 8,
                whiteSpace: 'pre-line',
                color: '#222',
                fontSize: 15
              }}
            >
              {typeof selectedCandidate.transcript_tec === 'string' ? selectedCandidate.transcript_tec : 'N/A'}
            </div>
          </div>
        </div>
      );
    }

    return (
      <Form form={techForm} layout='vertical' onFinish={handleSubmitTechFeedback}>
        {/* Tech form fields */}
        <Row gutter={16}>
          <Col span={8}>
            <Text type='secondary'>
              <UserOutlined /> Recruiter
            </Text>
            <Form.Item name='techRecruiter' rules={[{ required: true, message: 'Please input the recruiter name!' }]}>
              <Input placeholder='Enter Recruiter Name' />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Text type='secondary'>
              <CalendarOutlined /> Interview Date
            </Text>
            <Form.Item
              name='techInterviewDate'
              rules={[{ required: true, message: 'Please select the interview date!' }]}
            >
              <DatePicker showTime style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Text type='secondary'>
              <CalendarOutlined /> Feedback Date
            </Text>
            <Form.Item
              name='techFeedbackDate'
              dependencies={['techInterviewDate']}
              rules={[
                { required: false, message: 'Please select the feedback date!' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || !getFieldValue('techInterviewDate')) {
                      return Promise.resolve();
                    }
                    if (value.isAfter(getFieldValue('techInterviewDate'))) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('Feedback date must be after interview date!'));
                  }
                })
              ]}
            >
              <DatePicker showTime style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={8}>
            <Text type='secondary'>
              <UserOutlined /> Scheduled By
            </Text>
            <Form.Item
              name='techScheduledBy'
              rules={[{ required: false, message: 'Please input who scheduled the interview!' }]}
            >
              <Input placeholder='Enter Scheduler Name' />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Text type='secondary'>
              <CheckCircleOutlined /> Status
            </Text>
            <Form.Item name='techStatus' rules={[{ required: false, message: 'Please select the status!' }]}>
              <Select placeholder='Select Status'>
                <Select.Option value='scheduled'>scheduled</Select.Option>
                <Select.Option value='in_progress'>in_progress</Select.Option>
                <Select.Option value='completed'>completed</Select.Option>
                <Select.Option value='cancelled'>cancelled</Select.Option>
                <Select.Option value='not_scheduled'>not_scheduled</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Text type='secondary'>
              <CheckCircleOutlined /> Recommendation
            </Text>
            <Form.Item
              name='techRecommendation'
              rules={[{ required: true, message: 'Please select a recommendation!' }]}
            >
              <Select placeholder='Select Recommendation'>
                <Select.Option value='I recommend continuing the process'>
                  I recommend continuing the process
                </Select.Option>
                <Select.Option value='I do not recommend'>I do not recommend</Select.Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Text type='secondary'>
          <CommentOutlined /> Technical Comments
        </Text>
        <Form.Item label='' name='techComments'>
          <Input.TextArea rows={4} placeholder='Write here your note' />
        </Form.Item>

        <Text type='secondary'>
          <FileTextOutlined /> Transcript
        </Text>
        <Form.Item label='' name='techTranscript'>
          <Input.TextArea rows={4} placeholder='Write here your note' />
        </Form.Item>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item>
              <Button type='primary' htmlType='submit' loading={submittingTech} disabled={submittingTech}>
                {submittingTech ? 'Saving...' : 'Save & send'}
              </Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    );
  };

  if (loading)
    return (
      <Card style={{ padding: 24, borderRadius: 8, textAlign: 'center', maxWidth: 400, minWidth: 300 }}>
        <Spin size='large' />
      </Card>
    );

  // Helper function to get seniority tag color
  const getSeniorityColor = (seniority: string | undefined): string => {
    if (!seniority) return 'default';

    const lowerSeniority = seniority.toLowerCase();
    if (lowerSeniority.includes('junior')) return 'green';
    if (lowerSeniority.includes('mid')) return 'blue';
    if (lowerSeniority.includes('senior')) return 'purple';
    if (lowerSeniority.includes('lead')) return 'magenta';
    return 'default';
  };

  return (
    <div
      style={{
        width: '100%',
        maxWidth: 400,
        minWidth: 300,
        padding: 12,
        backgroundColor: '#fff',
        borderRadius: 8,
        overflowY: 'auto',
        maxHeight: 'calc(100vh - 200px)'
      }}
    >
      <div style={HEADER_STYLES.container}>
        <h3 style={HEADER_STYLES.title}>Selected Candidates</h3>
        <div style={HEADER_STYLES.countBadge}>{candidates.length}</div>
      </div>

      {candidates.map((candidate) => (
        <CandidateCard key={candidate.id} candidate={candidate} onClick={handleCandidateClick} />
      ))}

      <Drawer
        title='Feedback'
        placement='right'
        onClose={() => setSelectedCandidate(null)}
        open={!!selectedCandidate}
        width={800}
        destroyOnClose
      >
        {selectedCandidate && (
          <div>
            <CandidateHeader candidate={selectedCandidate} />
            <AnalysisSection candidate={selectedCandidate} />
            <NavigationPills activeTab={activeTab} onTabChange={setActiveTab} />
            <div>
              {activeTab === 'hr' && renderHRFeedbackContent()}
              {activeTab === 'tech' && renderTechFeedbackContent()}
            </div>
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default CandidateList;
