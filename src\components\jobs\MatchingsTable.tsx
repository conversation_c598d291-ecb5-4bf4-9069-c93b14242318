import React, { useState } from 'react';
import { Table, Checkbox, Button, message, Popover, Space, Row, Col, Switch, Dropdown, Menu, Tooltip } from 'antd';
import type { Breakpoint } from 'antd';
import { SearchOutlined, DownOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { ProgressCircle } from '../common/ProgressCircle';
import { useFetch } from '../../hooks/useFetch';
import type { MatchedCandidate } from '../../types/job';
import { endpoints } from '../../utils/api';
import { api } from '../../utils/api';

interface MatchingsTableProps {
  id: string;
  onSelectCandidate: (candidateId: string) => void;
}

type CandidateAnalysis = {
  candidate_id: string;
  analysis_data: any;
};

export const MatchingsTable: React.FC<MatchingsTableProps> = ({ id, onSelectCandidate }) => {
  const [limit, setLimit] = useState(10);
  const [hasFeedback, setHasFeedback] = useState(2);
  const [batchMode, setBatchMode] = useState(true);
  const [selectedCandidates, setSelectedCandidates] = useState<string[]>([]);
  const [isAdvancedSearchVisible, setIsAdvancedSearchVisible] = useState(false);
  const [tempLimit, setTempLimit] = useState(10);
  const [tempFeedback, setTempFeedback] = useState(2);
  const [tempBatchMode, setTempBatchMode] = useState(true);

  function fetchMatchings() {
    const { data, loading } = useFetch<{ matched_candidates: MatchedCandidate[] }>({
      url: endpoints.positions.match,
      method: 'POST',
      params: { position_id: id, limit, hasFeedback, batch_mode: batchMode },
      dependencies: [id, limit, hasFeedback, batchMode]
    });

    if (data) {
      // Sort by custom_analysis.compatibilityPercentage descending
      data.matched_candidates.sort((a, b) => {
        const aVal = a.custom_analysis?.compatibilityPercentage ?? 0;
        const bVal = b.custom_analysis?.compatibilityPercentage ?? 0;
        return bVal - aVal;
      });
    }

    return { data, loading };
  }

  const { data, loading } = fetchMatchings();

  const handleSelectCandidate = (candidateId: string) => {
    setSelectedCandidates((prev) => {
      if (prev.includes(candidateId)) {
        return prev.filter((id) => id !== candidateId);
      } else {
        return [...prev, candidateId];
      }
    });
  };

  const handleAddToInterview = async () => {
    try {
      if (selectedCandidates.length === 0) {
        message.warning('Please select at least one candidate to add to the interview.');
        return;
      }

      var interviewsAdd: CandidateAnalysis[] = [];

      selectedCandidates.forEach((id) => {
        var candidate = data?.matched_candidates.find((candidate) => candidate.id === id);
        if (!candidate) {
          message.error(`Candidate with ID ${id} not found.`);
          return;
        }
        interviewsAdd.push({
          candidate_id: candidate.id,
          analysis_data: {
            ...candidate.custom_analysis
          }
        });
      });

      if (interviewsAdd.length === 0) {
        message.error('No valid candidates selected.');
        return;
      }

      //const candidateParams = selectedCandidates.map((id) => `candidates_id=${id}`).join('&');
      const url = `${endpoints.interview.addCandidates(id)}`;

      const response = await api.post(url, interviewsAdd);

      if (response.status === 200) {
        message.success('Candidates added to interview successfully!');
      } else {
        message.error('Failed to add candidates to interview.');
      }
    } catch (error) {
      message.error('An error occurred. Please try again.');
    }
  };

  const handleAdvancedSearch = () => {
    setLimit(tempLimit);
    setHasFeedback(tempFeedback);
    setBatchMode(tempBatchMode);
    setIsAdvancedSearchVisible(false);
  };

  const candidateOptions = [5, 10, 15, 20, 25, 50, 100];

  const candidateMenu = (
    <Menu
      onClick={({ key }) => setTempLimit(Number(key))}
      selectedKeys={[tempLimit.toString()]}
      items={candidateOptions.map((option) => ({
        key: option.toString(),
        label: option.toString()
      }))}
    />
  );

  const columns = [
    {
      dataIndex: 'id',
      key: 'select',
      width: '5%',
      responsive: ['sm' as Breakpoint],
      render: (candidateId: string) => <Checkbox onChange={() => handleSelectCandidate(candidateId)} />
    },
    {
      title: 'Match%',
      dataIndex: ['custom_analysis', 'compatibilityPercentage'],
      key: 'compatibilityPercentage',
      width: '10%',
      responsive: ['md' as Breakpoint],
      render: (text: number) => (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
          <ProgressCircle
            percent={text || 0}
            size={44}
            strokeColor='#52C41A'
            strokeWidth={6}
            fontSize={12}
            fontWeight='bold'
            textColor='#000000'
          />
        </div>
      )
    },
    {
      title: 'Name',
      dataIndex: ['candidate_info', 'personal_info', 'full_name'],
      key: 'name',
      width: '15%',
      render: (fullName: string, record: MatchedCandidate) => (
        <span>{record.candidate_info.personal_info.full_name || 'N/A'}</span>
      )
    },
    {
      title: 'Matching Skills',
      key: 'skills',
      width: '30%',
      render: (value: unknown, record: MatchedCandidate) => {
        const skillsAnalysis = record.custom_analysis?.matchesFound;
        const unmatchedSkills = record.custom_analysis?.missing;

        return (
          <Space direction='vertical' size={8}>
            {skillsAnalysis && Object.keys(skillsAnalysis).length > 0 && (
              <div>
                <span style={{ color: '#52c41a', fontWeight: 500 }}>Matched: </span>
                <span style={{ color: '#52c41a' }}>{skillsAnalysis.join(', ')}</span>
              </div>
            )}
            {unmatchedSkills && unmatchedSkills.length > 0 && (
              <div>
                <span style={{ color: '#ff4d4f', fontWeight: 500 }}>Unmatched: </span>
                <span style={{ color: '#ff4d4f' }}>{unmatchedSkills.join(', ')}</span>
              </div>
            )}
            {(!skillsAnalysis || Object.keys(skillsAnalysis).length === 0) &&
              (!unmatchedSkills || unmatchedSkills.length === 0) &&
              'N/A'}
          </Space>
        );
      }
    },
    {
      title: 'Work Experience',
      dataIndex: ['candidate_info', 'work_experience'],
      key: 'work_experience',
      width: '20%',
      responsive: ['lg' as Breakpoint],
      render: (work_experience: { job_title: string }[]) =>
        work_experience ? work_experience.map((exp) => exp.job_title).join(', ') : 'N/A'
    },
    {
      title: 'Reason',
      dataIndex: ['custom_analysis', 'justification'],
      key: 'reason',
      width: '25%',
      responsive: ['lg' as Breakpoint],
      render: (text: string) => text || 'N/A'
    }
  ];

  const totalCandidates = data?.matched_candidates?.length || 0;

  const advancedSearchContent = (
    <div style={{ width: 300, padding: '12px' }}>
      {/* Number of candidates */}
      <Row align='middle' style={{ marginBottom: 12 }}>
        <Col span={12}>
          <span style={{ fontSize: '14px' }}>Number of candidates</span>
        </Col>
        <Col span={12} style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
          <Dropdown overlay={candidateMenu} trigger={['click']}>
            <Button
              style={{
                border: '1px solid #d9d9d9',
                borderRadius: '4px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                width: '60px',
                height: '28px',
                padding: '4px 8px',
                fontSize: '14px'
              }}
            >
              <span>{tempLimit}</span>
              <DownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Dropdown>
        </Col>
      </Row>

      {/* Feedback */}
      <Row style={{ marginBottom: 12 }}>
        <Col span={24}>
          <div style={{ marginBottom: 8, fontSize: '14px' }}>Feedback</div>
          <Space size={4}>
            <Button
              size='small'
              onClick={() => setTempFeedback(1)}
              style={{
                backgroundColor: tempFeedback === 1 ? '#6C63FF' : 'white',
                borderColor: tempFeedback === 1 ? '#6C63FF' : '#d9d9d9',
                color: tempFeedback === 1 ? 'white' : '#666',
                fontSize: '12px',
                height: '28px',
                borderRadius: '4px'
              }}
            >
              On
            </Button>
            <Button
              size='small'
              onClick={() => setTempFeedback(0)}
              style={{
                backgroundColor: tempFeedback === 0 ? '#6C63FF' : 'white',
                borderColor: tempFeedback === 0 ? '#6C63FF' : '#d9d9d9',
                color: tempFeedback === 0 ? 'white' : '#666',
                fontSize: '12px',
                height: '28px',
                borderRadius: '4px'
              }}
            >
              Off
            </Button>
            <Button
              size='small'
              onClick={() => setTempFeedback(2)}
              style={{
                backgroundColor: tempFeedback === 2 ? '#6C63FF' : 'white',
                borderColor: tempFeedback === 2 ? '#6C63FF' : '#d9d9d9',
                color: tempFeedback === 2 ? 'white' : '#666',
                fontSize: '12px',
                height: '28px',
                borderRadius: '4px'
              }}
            >
              Show all
            </Button>
          </Space>
        </Col>
      </Row>

      {/* Search button */}
      <Button
        type='primary'
        onClick={handleAdvancedSearch}
        style={{
          width: '100%',
          backgroundColor: '#6C63FF',
          borderColor: '#6C63FF',
          height: '32px',
          borderRadius: '4px',
          fontSize: '14px'
        }}
        icon={<SearchOutlined />}
      >
        Search
      </Button>
    </div>
  );

  return (
    <div>
      <Row justify='space-between' align='middle' style={{ marginBottom: 16, backgroundColor: '#fff', padding: '7px', borderRadius: '12px' }}>
        <Col>
          <span style={{ fontSize: '16px', fontWeight: 500 }}>{totalCandidates} Matching Candidates</span>
        </Col>
        <Col>
          <Space>
            <Popover
              content={advancedSearchContent}
              title='Advanced Search'
              trigger='click'
              open={isAdvancedSearchVisible}
              onOpenChange={setIsAdvancedSearchVisible}
              placement='bottomRight'
              overlayStyle={{ zIndex: 1000 }}
            >
              <Button icon={<SearchOutlined />} onClick={() => setIsAdvancedSearchVisible(!isAdvancedSearchVisible)}>
                Advanced search
              </Button>
            </Popover>
            <Button
              type='primary'
              style={{ backgroundColor: '#6C63FF', borderColor: '#6C63FF' }}
              onClick={handleAddToInterview}
            >
              Add to Interview
            </Button>
          </Space>
        </Col>
      </Row>

      <Table
        columns={columns}
        dataSource={data?.matched_candidates || []}
        loading={loading}
        rowKey='id'
        pagination={{ pageSize: limit }}
      />
    </div>
  );
};
