import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import type { Job } from '../types/job';

interface JobContextType {
  jobId: string | null;
  setJobId: (id: string) => void;
  jobData: Job | null;
  setJobData: (job: Job) => void;
  selectedCandidates: string[];
  toggleCandidateSelection: (candidateId: string) => void;
}

const JobContext = createContext<JobContextType | undefined>(undefined);

export const JobProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const location = useLocation();
  const [jobId, setJobId] = useState<string | null>(null);
  const [jobData, setJobData] = useState<Job | null>(null);
  const [selectedCandidates, setSelectedCandidates] = useState<string[]>([]);

  useEffect(() => {
    const job = location.state?.job as Job;
    if (job) {
      setJobId(job.id);
      setJobData(job);
    }
  }, [location.state]);

  const toggleCandidateSelection = (candidateId: string) => {
    setSelectedCandidates((prev) =>
      prev.includes(candidateId) ? prev.filter((id) => id !== candidateId) : [...prev, candidateId]
    );
  };

  return (
    <JobContext.Provider value={{ jobId, setJobId, jobData, setJobData, selectedCandidates, toggleCandidateSelection }}>
      {children}
    </JobContext.Provider>
  );
};

export const useJobContext = () => {
  const context = useContext(JobContext);
  if (!context) {
    throw new Error('useJobContext must be used within a JobProvider');
  }
  return context;
};
