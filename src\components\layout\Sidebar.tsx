import React from 'react';
import { FileTextOutlined, TeamOutlined, RobotOutlined, UserSwitchOutlined } from '@ant-design/icons';
import { useJobContext } from '../../contexts/JobContext';

interface SidebarProps {
  onSelect: (key: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ onSelect }) => {
  const [selectedTab, setSelectedTab] = React.useState('job-description');
  const { jobData } = useJobContext();

  const handleClick = (key: string) => {
    setSelectedTab(key);
    onSelect(key);
  };

  // Check if job is open (no reason status means open)
  const isJobOpen = !jobData?.position_info?.reasonStatus?.reason;

  const menuItems = [
    {
      key: 'job-description',
      icon: <FileTextOutlined />,
      label: 'Job Description'
    },
    {
      key: 'matching-candidates',
      icon: <TeamOutlined />,
      label: 'Smart Matching'
    },
    // Only show Custom Matching for open positions
    ...(isJobOpen ? [{
      key: 'manual-matching',
      icon: <UserSwitchOutlined />,
      label: 'Custom Matching'
    }] : []),
    {
      key: 'generate-ai-interview',
      icon: <RobotOutlined />,
      label: 'Generate AI Interview'
    }
  ];

  return (
    <div
      style={{
        width: 'fill',
        minWidth: '232px',
        backgroundColor: '#fff',
        borderRight: '1px solid #f0f0f0',
        borderRadius: '12px',
        overflowY: 'auto',
        maxHeight: 'calc(100vh - 200px)'
      }}
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', padding: '16px' }}>
        {menuItems.map((item) => (
          <div
            key={item.key}
            onClick={() => handleClick(item.key)}
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '0 16px',
              height: '40px',
              cursor: 'pointer',
              backgroundColor: selectedTab === item.key ? '#E6EFFF' : 'transparent',
              borderRadius: '50px',
              color: selectedTab === item.key ? '#6E6EFD' : '#1F2937',
              fontWeight: selectedTab === item.key ? 500 : 400,
              boxSizing: 'border-box'
            }}
          >
            <span
              style={{
                marginRight: 12,
                fontSize: 18,
                color: selectedTab === item.key ? '#6E6EFD' : '#1F2937'
              }}
            >
              {item.icon}
            </span>
            {item.label}
          </div>
        ))}
      </div>
    </div>
  );
};

export default Sidebar;
