import React from 'react';
import { Drawer, Row, Col, Tag, Typography } from 'antd';
import { UserOutlined, CalendarOutlined, CheckCircleOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface CandidateFeedbackDrawerProps {
  open: boolean;
  onClose: () => void;
  feedback: any;
  candidateInfo: any;
}

export const CandidateFeedbackDrawer: React.FC<CandidateFeedbackDrawerProps> = ({
  open,
  onClose,
  feedback,
  candidateInfo
}) => {
  if (!feedback || !candidateInfo) return null;

  // Status and recommendation tags
  let statusColor = 'default';
  if (feedback.status_hr === 'Interview Conducted') statusColor = 'red';
  else if (feedback.status_hr === 'Scheduled') statusColor = 'blue';
  else if (feedback.status_hr === 'Pending') statusColor = 'orange';
  else if (feedback.status_hr) statusColor = 'geekblue';

  const recColor = feedback.recommendation_hr ? 'cyan' : 'default';

  const statusText = typeof feedback.status_tec === 'string' ? feedback.status_tec : 'N/A';
  const recommendationText = feedback.recommendation_tec === true ? 'Recommended' : 'Not Recommended';

  return (
    <Drawer
      width='60vw'
      placement='right'
      onClose={onClose}
      open={open}
      title={null}
      styles={{ body: { padding: 0 } }}
      style={{ maxWidth: '100vw' }}
    >
      <div style={{ padding: 32, background: '#fff', minHeight: '100%', wordBreak: 'break-word', maxWidth: '100%' }}>
        <Row align='middle' gutter={24} style={{ marginBottom: 24, flexWrap: 'wrap' }}>
          <Col>
            <div
              style={{
                width: 64,
                height: 64,
                borderRadius: '50%',
                background: '#6c63ff',
                color: '#fff',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: 28,
                fontWeight: 600
              }}
            >
              {(() => {
                const name = candidateInfo.personal_info?.full_name || '';
                const parts = name.trim().split(' ');
                if (parts.length === 0 || !parts[0]) return '?';
                if (parts.length === 1) return parts[0][0].toUpperCase();
                return (parts[0][0] + parts[parts.length - 1][0]).toUpperCase();
              })()}
            </div>
          </Col>
          <Col flex='auto'>
            <Title level={4} style={{ margin: 0 }}>
              {candidateInfo.personal_info?.full_name}
            </Title>
            <Text type='secondary'>{feedback.position_info?.roleName || ''}</Text>
          </Col>
        </Row>
        <Row gutter={16} style={{ marginBottom: 16, flexWrap: 'wrap' }}>
          <Col span={6}>
            <Text type='secondary'>
              <UserOutlined /> Recruiter
            </Text>
            <div style={{ fontWeight: 500 }}>{feedback.recruiter_tec_id || 'N/A'}</div>
          </Col>
          <Col span={6}>
            <Text type='secondary'>
              <CalendarOutlined /> Interview Date
            </Text>
            <div style={{ fontWeight: 500 }}>{feedback.interview_date_tec?.substr(0, 10) || 'N/A'}</div>
          </Col>
          <Col span={6}>
            <Text type='secondary'>
              <UserOutlined /> Scheduled By
            </Text>
            <div style={{ fontWeight: 500 }}>{feedback.scheduled_tec_id || 'N/A'}</div>
          </Col>
        </Row>
        <Row gutter={16} style={{ marginBottom: 16, flexWrap: 'wrap' }}>
          <Col span={6}>
            <Text type='secondary'>
              <CalendarOutlined /> Feedback Date
            </Text>
            <div style={{ fontWeight: 500 }}>{feedback.feedback_date_tec?.substr(0, 10) || 'N/A'}</div>
          </Col>
          <Col span={6}>
            <Text type='secondary'>
              <CheckCircleOutlined /> Status
            </Text>
            <div>
              <Tag color={statusColor} style={{ fontWeight: 600, fontSize: 14, padding: '2px 12px' }}>
                {statusText}
              </Tag>
            </div>
          </Col>
          <Col span={6}>
            <Text type='secondary'>
              <CheckCircleOutlined /> Recommendation
            </Text>
            <div>
              <Tag color={recColor} style={{ fontWeight: 600, fontSize: 14, padding: '2px 12px' }}>
                {recommendationText}
              </Tag>
            </div>
          </Col>
        </Row>
        <div
          style={{
            background: '#f6f7fb',
            borderRadius: 7,
            padding: 16,
            marginBottom: 16,
            border: '1px solid #e6e6e6',
            wordBreak: 'break-word',
            maxWidth: '100%'
          }}
        >
          <Text strong style={{ color: '#6c63ff' }}>
            Comments
          </Text>
          <div style={{ maxHeight: 80, overflowY: 'auto', marginTop: 8, color: '#222', fontSize: 15 }}>
            {typeof feedback.feedback_tec?.additionalProp1 === 'string'
              ? feedback.feedback_tec?.additionalProp1
              : 'N/A'}
          </div>
        </div>
        <div
          style={{
            background: '#f6f7fb',
            borderRadius: 7,
            padding: 16,
            border: '1px solid #e6e6e6',
            wordBreak: 'break-word',
            maxWidth: '100%'
          }}
        >
          <Text strong style={{ color: '#6c63ff' }}>
            Transcript
          </Text>
          <div
            style={{
              maxHeight: 120,
              overflowY: 'auto',
              marginTop: 8,
              whiteSpace: 'pre-line',
              color: '#222',
              fontSize: 15
            }}
          >
            {typeof feedback.transcript_tec === 'string' ? feedback.transcript_tec : 'N/A'}
          </div>
        </div>
      </div>
    </Drawer>
  );
};
