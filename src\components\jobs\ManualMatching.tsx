import React, { useState, useEffect } from 'react';
import { Table, Button, message, Row, Col, Typography, Space, Input } from 'antd';
import { ProgressCircle } from '../common/ProgressCircle';
import { useFetch } from '../../hooks/useFetch';
import { useDebounce } from '../../hooks/useDebounce';
import { useJobContext } from '../../contexts/JobContext';
import { TableFilterController } from '../common/TableFilterController';
import { FilterOptionsService } from '../../services/filterOptionsService';
import type { Candidate } from '../../types/candidate';
import type { CandidateFilter, FilterOption } from '../../types/filters';
import type { MatchedCandidate } from '../../types/job';
import { endpoints, api } from '../../utils/api';
import { formatDateTime } from '../../utils/dateUtils';

const { Search } = Input;
const { Title, Text } = Typography;

interface CandidatesResponse {
  total_items: number;
  items: Candidate[];
}

interface ManualMatchingProps {
  id: string;
}

export const ManualMatching: React.FC<ManualMatchingProps> = ({ id }) => {
  const { jobData } = useJobContext();
  const [selectedCandidates, setSelectedCandidates] = useState<string[]>([]);
  const [matchingResults, setMatchingResults] = useState<MatchedCandidate[] | null>(null);
  const [isMatching, setIsMatching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  
  // Pagination and search state
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState<string | null>(null);
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useDebounce('', 300);
  
  // Filter state (no status since we always filter to enabled candidates)
  const [filters, setFilters] = useState<CandidateFilter>({
    status: 'enabled', // Always enabled, but hidden from UI
    role: '',
    country: '',
    createdBy: '',
    createdFrom: '',
    createdTo: '',
    searchTerm: ''
  });
  
  // Filter options state
  const [filterOptions, setFilterOptions] = useState<{
    roles: FilterOption[];
    countries: FilterOption[];
    createdBy: FilterOption[];
  }>({ roles: [], countries: [], createdBy: [] });
  const [filterOptionsLoading, setFilterOptionsLoading] = useState(false);



  // Fetch candidates with filtering (excluding disabled candidates)
  const { data, loading, error } = useFetch<CandidatesResponse>({
    url: endpoints.candidates.list,
    method: 'POST',
    params: {
      chunk_size: limit,
      page
    },
    body: {
      status: true, // Always exclude disabled candidates
      search_term: debouncedSearchTerm ? searchTerm?.toLowerCase() : '',
      country: filters.country,
      role: filters.role,
      created_by: filters.createdBy,
      created_from: filters.createdFrom,
      created_to: filters.createdTo
    },
    dependencies: [
      limit,
      page,
      debouncedSearchTerm,
      filters.country,
      filters.role,
      filters.createdBy,
      filters.createdFrom,
      filters.createdTo
    ]
  });

  // Load filter options
  useEffect(() => {
    const loadFilterOptions = async () => {
      setFilterOptionsLoading(true);
      try {
        const [roles, countries, createdBy] = await Promise.all([
          FilterOptionsService.getRoles(),
          FilterOptionsService.getCountries(),
          FilterOptionsService.getCreatedBy()
        ]);
        setFilterOptions({ roles, countries, createdBy });
      } catch (error) {
        console.error('Error loading filter options:', error);
      } finally {
        setFilterOptionsLoading(false);
      }
    };

    loadFilterOptions();
  }, []);

  // Update debounced search term
  useEffect(() => {
    setDebouncedSearchTerm(searchTerm || '');
    setFilters((prev) => ({ ...prev, searchTerm: searchTerm || '' }));
    setPage(1);
  }, [searchTerm]);

  // Handle candidate selection
  const handleSelectCandidate = (candidateId: string) => {
    setSelectedCandidates((prev) =>
      prev.includes(candidateId)
        ? prev.filter((id) => id !== candidateId)
        : [...prev, candidateId]
    );
  };

  // Handle select all for current page
  const handleSelectAll = () => {
    const currentPageCandidateIds = data?.items?.map(candidate => candidate.id) || [];
    const currentPageSelected = currentPageCandidateIds.filter(id => selectedCandidates.includes(id));

    if (currentPageSelected.length === currentPageCandidateIds.length) {
      // Deselect all candidates from current page
      setSelectedCandidates(prev => prev.filter(id => !currentPageCandidateIds.includes(id)));
    } else {
      // Select all candidates from current page
      setSelectedCandidates(prev => {
        const newSelections = currentPageCandidateIds.filter(id => !prev.includes(id));
        return [...prev, ...newSelections];
      });
    }
  };

  // Handle clear selection (all pages)
  const handleClearSelection = () => {
    setSelectedCandidates([]);
  };

  // Get current page selection info
  const getCurrentPageSelectionInfo = () => {
    const currentPageCandidateIds = data?.items?.map(candidate => candidate.id) || [];
    const currentPageSelected = currentPageCandidateIds.filter(id => selectedCandidates.includes(id));
    return {
      currentPageCandidateIds,
      currentPageSelected,
      isAllCurrentPageSelected: currentPageSelected.length === currentPageCandidateIds.length && currentPageCandidateIds.length > 0,
      isPartialCurrentPageSelected: currentPageSelected.length > 0 && currentPageSelected.length < currentPageCandidateIds.length
    };
  };

  // Filter handlers
  const handleFiltersChange = (newFilters: CandidateFilter) => {
    setFilters(newFilters);
  };

  const handleClearFilters = () => {
    setFilters({
      status: 'enabled', // Keep status as enabled (hidden from UI)
      role: '',
      country: '',
      createdBy: '',
      createdFrom: '',
      createdTo: '',
      searchTerm: ''
    });
    setPage(1);
  };

  const handleApplyFilters = () => {
    setPage(1);
    // Note: We keep selections when applying filters since they might be from other filter combinations
  };

  // Handle manual matching
  const handleMatchCandidates = async () => {
    if (selectedCandidates.length === 0) {
      message.warning('Please select at least one candidate to match.');
      return;
    }

    setIsMatching(true);
    try {
      // Use the custom matching endpoint with selected candidate IDs
      // Build query string manually to handle array parameters correctly
      const candidateParams = selectedCandidates.map(candidateId => `candidates_id=${candidateId}`).join('&');
      const url = `${endpoints.positions.customMatch}?position_id=${id}&${candidateParams}`;

      const response = await api.get(url);

      if (response.data) {
        // The response contains matched_candidates array
        const matchedCandidates = response.data.matched_candidates || response.data;

        if (!Array.isArray(matchedCandidates)) {
          message.error('Invalid response format from server.');
          return;
        }

        // Sort by compatibility percentage descending (use custom_analysis as primary)
        matchedCandidates.sort((a: any, b: any) => {
          const aVal = a.custom_analysis?.compatibilityPercentage ?? a.analysis?.compatibilityPercentage ?? 0;
          const bVal = b.custom_analysis?.compatibilityPercentage ?? b.analysis?.compatibilityPercentage ?? 0;
          return bVal - aVal;
        });

        if (matchedCandidates.length === 0) {
          message.warning('No matching results found for the selected candidates.');
          setMatchingResults([]);
        } else {
          setMatchingResults(matchedCandidates);
          message.success(`Successfully matched ${matchedCandidates.length} candidates!`);
        }
        setShowResults(true);
      } else {
        message.error('No matching results returned from the server.');
      }
    } catch (error: any) {
      console.error('Error matching candidates:', error);
      console.error('Error response:', error.response?.data);

      if (error.response?.status === 404) {
        message.error('Matching service not found. Please contact support.');
      } else if (error.response?.status === 422) {
        const errorMessage = error.response?.data?.detail || error.response?.data?.message || 'Invalid request format';
        message.error(`Request validation failed: ${errorMessage}`);
      } else if (error.response?.status === 500) {
        message.error('Server error occurred during matching. Please try again later.');
      } else if (error.code === 'NETWORK_ERROR') {
        message.error('Network error. Please check your connection and try again.');
      } else {
        message.error('Failed to match candidates. Please try again.');
      }
    } finally {
      setIsMatching(false);
    }
  };

  // Error handling
  if (error) {
    return (
      <div style={{ padding: 24, textAlign: 'center' }}>
        <Text type="danger">Error loading candidates: {error}</Text>
        <br />
        <Button
          type="primary"
          style={{ marginTop: 16 }}
          onClick={() => window.location.reload()}
        >
          Retry
        </Button>
      </div>
    );
  }

  // Check if job is closed
  if (jobData?.position_info?.reasonStatus?.reason) {
    return (
      <div style={{ padding: 24, textAlign: 'center' }}>
        <Text type="warning">Custom Matching is only available for open positions.</Text>
        <br />
        <Text type="secondary">This position is closed: {jobData.position_info.reasonStatus.reason}</Text>
      </div>
    );
  }

  const totalCandidates = data?.items?.length || 0;
  const selectedCount = selectedCandidates.length;

  // Candidate table columns
  const candidateColumns = [
    {
      title: 'Full Name',
      key: 'fullName',
      width: '20%',
      render: (_: any, record: Candidate) => (
        <div>
          <Text strong>{record.candidate_info?.personal_info?.full_name || 'N/A'}</Text>
        </div>
      )
    },
    {
      title: 'Role/Position',
      key: 'role',
      width: '15%',
      render: (_: any, record: Candidate) => {
        const roles = record.candidate_info?.roles || [];
        return roles.length > 0 ? roles.join(', ') : 'N/A';
      }
    },
    {
      title: 'Email',
      key: 'email',
      width: '20%',
      render: (_: any, record: Candidate) => (
        <Text>{record.candidate_info?.personal_info?.email || 'N/A'}</Text>
      )
    },
    {
      title: 'Country/Location',
      key: 'location',
      width: '15%',
      render: (_: any, record: Candidate) => (
        <Text>{record.candidate_info?.personal_info?.country || 'N/A'}</Text>
      )
    },
    {
      title: 'Created By',
      key: 'createdBy',
      width: '15%',
      render: (_: any, record: Candidate) => (
        <Text>{record.created_by || 'N/A'}</Text>
      )
    },
    {
      title: 'Created Date',
      key: 'createdDate',
      width: '15%',
      render: (_: any, record: Candidate) => (
        <Text>{record.created_at ? formatDateTime(record.created_at) : 'N/A'}</Text>
      )
    }
  ];

  // Add state for selected results candidates
  const [selectedResultsCandidates, setSelectedResultsCandidates] = useState<string[]>([]);

  // Handle results candidate selection
  const handleSelectResultsCandidate = (candidateId: string) => {
    setSelectedResultsCandidates((prev) =>
      prev.includes(candidateId)
        ? prev.filter((id) => id !== candidateId)
        : [...prev, candidateId]
    );
  };

  // Handle add to interview (same as MatchingsTable)
  const handleAddToInterview = async () => {
    try {
      if (selectedResultsCandidates.length === 0) {
        message.warning('Please select at least one candidate to add to the interview.');
        return;
      }

      const interviewsAdd: any[] = [];

      selectedResultsCandidates.forEach((candidateId) => {
        const candidate = matchingResults?.find((candidate: any) => candidate.id === candidateId);
        if (!candidate) {
          message.error(`Candidate with ID ${candidateId} not found.`);
          return;
        }
        interviewsAdd.push({
          candidate_id: candidate.id,
          analysis_data: {
            ...candidate.custom_analysis
          }
        });
      });

      if (interviewsAdd.length === 0) {
        message.error('No valid candidates selected.');
        return;
      }

      const url = `${endpoints.interview.addCandidates(id)}`;
      const response = await api.post(url, interviewsAdd);

      if (response.status === 200) {
        message.success('Candidates added to interview successfully!');
        setSelectedResultsCandidates([]);
      } else {
        message.error('Failed to add candidates to interview.');
      }
    } catch (error) {
      console.error('Error adding candidates to interview:', error);
      message.error('An error occurred. Please try again.');
    }
  };

  // Matching results table columns (same format as MatchingsTable)
  const resultsColumns = [
    {
      dataIndex: 'id',
      key: 'select',
      width: '5%',
      render: (candidateId: string) => (
        <input
          type="checkbox"
          checked={selectedResultsCandidates.includes(candidateId)}
          onChange={() => handleSelectResultsCandidate(candidateId)}
        />
      )
    },
    {
      title: 'Match%',
      key: 'compatibilityPercentage',
      width: '10%',
      render: (_: any, record: any) => {
        const percentage = record.custom_analysis?.compatibilityPercentage ?? record.analysis?.compatibilityPercentage ?? 0;
        return (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <ProgressCircle
              percent={percentage}
              size={44}
              strokeColor='#52C41A'
              strokeWidth={6}
              fontSize={12}
              fontWeight='bold'
              textColor='#000000'
            />
          </div>
        );
      }
    },
    {
      title: 'Name',
      key: 'name',
      width: '15%',
      render: (_: any, record: any) => (
        <Text strong>{record.candidate_info?.personal_info?.full_name || 'N/A'}</Text>
      )
    },
    {
      title: 'Matching Skills',
      key: 'skills',
      width: '30%',
      render: (_: any, record: any) => {
        const skillsAnalysis = record.custom_analysis?.matchesFound || record.analysis?.matchesFound || [];
        const unmatchedSkills = record.custom_analysis?.missing || record.analysis?.missing || [];

        return (
          <Space direction='vertical' size={8}>
            {skillsAnalysis && skillsAnalysis.length > 0 && (
              <div>
                <span style={{ color: '#52c41a', fontWeight: 500 }}>Matched: </span>
                <span style={{ color: '#52c41a' }}>{skillsAnalysis.join(', ')}</span>
              </div>
            )}
            {unmatchedSkills && unmatchedSkills.length > 0 && (
              <div>
                <span style={{ color: '#ff4d4f', fontWeight: 500 }}>Unmatched: </span>
                <span style={{ color: '#ff4d4f' }}>{unmatchedSkills.join(', ')}</span>
              </div>
            )}
            {(!skillsAnalysis || skillsAnalysis.length === 0) &&
              (!unmatchedSkills || unmatchedSkills.length === 0) &&
              'N/A'}
          </Space>
        );
      }
    },
    {
      title: 'Work Experience',
      key: 'work_experience',
      width: '20%',
      render: (_: any, record: any) => {
        const workExperience = record.candidate_info?.work_experience || [];
        return workExperience.length > 0
          ? workExperience.map((exp: any) => exp.job_title).join(', ')
          : 'N/A';
      }
    },
    {
      title: 'Reason',
      key: 'reason',
      width: '20%',
      render: (_: any, record: any) => {
        const justification = record.custom_analysis?.justification || record.analysis?.justification || 'N/A';
        return (
          <Text style={{ fontSize: '12px' }}>{justification}</Text>
        );
      }
    }
  ];

  return (
    <div>
      {/* Job Title Header */}
      <div style={{ marginBottom: 24 }}>
        <Title level={3} style={{ marginBottom: 8 }}>
          Custom Matching
        </Title>
      </div>

      {!showResults ? (
        <>
          {/* Search and Filters */}
          <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
            <Col>
              <Space>
                <Search
                  style={{ width: '300px' }}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onSearch={setSearchTerm}
                  placeholder="Search candidates by name or keywords"
                  value={searchTerm || ''}
                  enterButton
                />
                <TableFilterController
                  filterType="candidate"
                  filters={filters}
                  onFiltersChange={handleFiltersChange}
                  onClearFilters={handleClearFilters}
                  onApplyFilters={handleApplyFilters}
                  availableOptions={filterOptions}
                  loading={filterOptionsLoading}
                  filterConfig={{
                    status: { visible: false }, // Hide status filter since we only show enabled candidates
                    role: { visible: true, label: 'Role', placeholder: 'Select role' },
                    country: { visible: true, label: 'Location', placeholder: 'Select location' },
                    createdBy: { visible: true, label: 'Created By', placeholder: 'Select created by' },
                    createdFrom: { visible: true, label: 'Date from', placeholder: 'Select date' },
                    createdTo: { visible: true, label: 'to', placeholder: 'Select date' }
                  }}
                />
              </Space>
            </Col>
          
            <Col>
              <Button
                type="primary"
                size="large"
                onClick={handleMatchCandidates}
                loading={isMatching}
                disabled={selectedCount === 0}
                style={{ backgroundColor: '#6C63FF', borderColor: '#6C63FF' }}
              >
                Match Candidates ({selectedCount})
              </Button>
            </Col>
          </Row>

          {/* Selection Summary */}
          {selectedCount > 0 && (
            <div style={{
              marginBottom: 16,
              padding: 12,
              background: '#f0f9ff',
              border: '1px solid #bae6fd',
              borderRadius: 6,
              fontSize: '14px'
            }}>
              <Text style={{ color: '#0369a1' }}>
                <strong>{selectedCount}</strong> candidates selected for matching
                {(() => {
                  const currentPageSelected = selectedCandidates.filter(id =>
                    data?.items?.some(item => item.id === id)
                  ).length;
                  const otherPagesSelected = selectedCount - currentPageSelected;

                  if (otherPagesSelected > 0) {
                    return ` (${currentPageSelected} on this page, ${otherPagesSelected} from other pages)`;
                  }
                  return '';
                })()}
              </Text>
            </div>
          )}

          {/* Candidates Table */}
          <Table
            loading={loading}
            columns={candidateColumns}
            dataSource={data?.items || []}
            rowKey="id"
            locale={{
              emptyText: totalCandidates === 0 && !loading ?
                'No eligible candidates found. Try adjusting your filters.' :
                'No data'
            }}
            pagination={{
              current: page,
              total: data?.total_items,
              pageSize: limit,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} of ${total} candidates`,
              onChange: (newPage, newPageSize) => {
                setPage(newPage);
                if (newPageSize !== limit) {
                  setLimit(newPageSize);
                  setPage(1);
                }
              }
            }}
            rowSelection={{
              selectedRowKeys: data?.items?.map(candidate => candidate.id).filter(id => selectedCandidates.includes(id)) || [],
              onChange: (selectedRowKeys) => {
                // This handles individual row selection changes
                const currentPageIds = data?.items?.map(candidate => candidate.id) || [];
                const otherPagesSelected = selectedCandidates.filter(id => !currentPageIds.includes(id));
                setSelectedCandidates([...otherPagesSelected, ...selectedRowKeys as string[]]);
              },
              onSelect: (record) => {
                handleSelectCandidate(record.id);
              },
              onSelectAll: () => {
                handleSelectAll();
              },
              getCheckboxProps: (record) => ({
                name: record.id,
              }),
            }}
          />
        </>
      ) : (
        <>
          {/* Results Header */}
          <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
            <Col>
              <Text style={{ fontSize: 16, fontWeight: 500 }}>
                {matchingResults?.length || 0} Matching Results
              </Text>
            </Col>
            <Col>
              <Space>
                <Button
                  type="primary"
                  style={{ backgroundColor: '#6C63FF', borderColor: '#6C63FF' }}
                  onClick={handleAddToInterview}
                  disabled={selectedResultsCandidates.length === 0}
                >
                  Add to Interview ({selectedResultsCandidates.length})
                </Button>
                <Button onClick={() => {
                  setShowResults(false);
                  setSelectedResultsCandidates([]);
                  setMatchingResults(null);
                }}>
                  Back to Candidate Selection
                </Button>
              </Space>
            </Col>
          </Row>

          {/* Results Table */}
          <Table
            columns={resultsColumns}
            dataSource={matchingResults || []}
            loading={isMatching}
            rowKey="id"
            pagination={false}
            style={{ background: '#fff', borderRadius: 8 }}
            locale={{
              emptyText: matchingResults?.length === 0 && !isMatching ?
                'No matching results found for the selected candidates.' :
                'No data'
            }}
          />
        </>
      )}
    </div>
  );
};
