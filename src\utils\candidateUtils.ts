import type { Candidate } from '../types/candidate';

/**
 * Calculate resume percentage from analysis data
 */
export const getResumePercentage = (candidate: Candidate): number => {
  return candidate.analysis_data?.compatibilityPercentage || 0;
};

/**
 * Calculate tech interview percentage from interview data
 */
export const getTechInterviewPercentage = (candidate: Candidate): number => {
  return candidate.interview_data?.percentage_of_match
    ? Math.round(candidate.interview_data.percentage_of_match)
    : 0;
};

/**
 * Get candidate initials for avatar
 */
export const getCandidateInitials = (fullName: string): string => {
  const name = fullName || '';
  const parts = name.trim().split(' ');
  if (parts.length === 0 || !parts[0]) return '?';
  if (parts.length === 1) return parts[0][0].toUpperCase();
  return (parts[0][0] + parts[parts.length - 1][0]).toUpperCase();
};

/**
 * Get seniority color based on seniority level
 */
export const getSeniorityColor = (seniority?: string): string => {
  if (!seniority) return 'default';

  const lowerSeniority = seniority.toLowerCase();
  if (lowerSeniority.includes('junior')) return 'green';
  if (lowerSeniority.includes('mid')) return 'blue';
  if (lowerSeniority.includes('senior')) return 'purple';
  if (lowerSeniority.includes('lead')) return 'magenta';
  return 'default';
};

/**
 * Format interview status color
 */
export const getStatusColor = (status?: string): string => {
  if (status === 'Interview Conducted') return 'red';
  if (status === 'Scheduled') return 'blue';
  if (status === 'Pending') return 'orange';
  if (status === 'completed') return 'green';
  if (status) return 'geekblue';
  return 'default';
};

/**
 * Format recommendation color
 */
export const getRecommendationColor = (recommendation?: boolean): string => {
  return recommendation ? 'cyan' : 'default';
};

/**
 * Format recommendation text
 */
export const getRecommendationText = (recommendation?: boolean): string => {
  return recommendation === true ? 'Recommended' : 'Not Recommended';
};

/**
 * Create API payload for HR feedback
 */
export const createHRFeedbackPayload = (
  positionId: string,
  candidate: Candidate,
  values: any
) => ({
  position_id: positionId,
  candidate_id: candidate.candidate_id,
  recruiter_hr_id: values.hrRecruiter,
  scheduled_hr_id: values.hrScheduledBy,
  candidate_info: candidate.candidate_info,
  feedback_hr: { comments: values.hrComments },
  interview_date_hr: values.hrInterviewDate ? values.hrInterviewDate.toISOString() : null,
  feedback_date_hr: values.hrFeedbackDate ? values.hrFeedbackDate.toISOString() : new Date().toISOString(),
  status_hr: values.hrStatus, // 'completed', // Always set to completed when saving feedback
  recommendation_hr: values.hrRecommendation === 'I recommend continuing the process',
  transcript_hr: values.hrTranscript || ''
});

/**
 * Create API payload for Tech feedback
 */
export const createTechFeedbackPayload = (
  positionId: string,
  candidate: Candidate,
  values: any
) => ({
  position_id: positionId,
  candidate_id: candidate.candidate_id,
  recruiter_tec_id: values.techRecruiter,
  scheduled_tec_id: values.techScheduledBy,
  feedback_tec: { additionalProp1: values.techComments },
  interview_date_tec: values.techInterviewDate ? values.techInterviewDate.toISOString() : null,
  feedback_date_tec: values.techFeedbackDate ? values.techFeedbackDate.toISOString() : new Date().toISOString(),
  status_tec: values.techStatus, // 'completed', // Always set to completed when saving feedback
  recommendation_tec: values.techRecommendation === 'I recommend continuing the process',
  transcript_tec: values.techTranscript || ''
});

/**
 * Extract feedback comments value safely
 */
export const extractFeedbackComments = (feedbackData: any): string => {
  if (!feedbackData) return '';
  if (typeof feedbackData === 'string') return feedbackData;
  if (typeof feedbackData === 'object') {
    return feedbackData.comments || feedbackData.additionalProp1 || '';
  }
  return '';
};